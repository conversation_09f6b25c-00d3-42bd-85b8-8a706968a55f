<?php

namespace App\Modifiers;

use Statamic\Modifiers\Modifier;

class TrailingSlash extends Modifier
{
    /**
     * Modify a value.
     *
     * @param mixed  $value    The value to be modified
     * @param array  $params   Any parameters used in the modifier
     * @param array  $context  Contextual values
     * @return mixed
     */
    public function index($value, $params, $context)
    {
        // If URL is an full url or ends in /, do nothing
		if (str_starts_with($value, "http") || str_ends_with($value, "/")) {
			return $value;
		}

		// In production mode return ending slash
		return $value . '/';
    }
}
