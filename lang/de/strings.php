<?php
return [
    // Contact form
    'form_honeypot'                     => 'Fax',
    'form_mail_body_owner'              => 'Neue Anfrage über Kontaktformular',
    'form_mail_body_sender'             => 'Vielen Dank für Ihre Nachricht. Wir werden uns in Kürze bei Ihnen melden.',
    'form_mail_closing'                 => 'Herzliche Grüße',
    'form_mail_from'                    => config('app.name'),
    'form_mail_greeting'                => 'Hallo',
    'form_mail_logo'                    => 'https://cdn.studio1902.nl/assets/statamic-peak/statamic-peak-logo.png',
    'form_mail_submitted'               => 'gesendet',
    'form_mail_title'                   => 'Kontaktformular',
    'form_mail_url'                     => config('app.url'),
    'form_send'                         => 'Senden',
    'form_success'                      => 'Vielen Dank für Ihre Nachricht. Wir werden uns in Kürze bei Ihnen melden.',

    // Cookie banner
    'cookie_title'                      => 'Cookies 🍪',
    'cookie_functional'                 => 'Funktional',
    'cookie_functional_explanation'     => 'Funktionale Cookies sind für die Funktionsfähigkeit der Website notwendig. Diese Cookies Können nicht deaktiviert werden.',
    'cookie_analytics'                  => 'Analyse',
    'cookie_analytics_explanation'      => 'Analyse-Cookies helfen Webseiten-Besitzern zu verstehen, wie Besucher mit Webseiten interagieren, indem Informationen anonym gesammelt und gemeldet werden.',
    'cookie_ads'                        => 'Werbung & Tracking',
    'cookie_ads_explanation'            => 'Marketing-Cookies werden verwendet, um Besuchern auf Webseiten zu folgen und ggf. relevante Werbung anzuzeigen.',
    'cookie_accept'                     => 'Akzeptieren',
    'cookie_ignore'                     => 'Ignorieren',
    'cookie_explanation'                => 'Diese Website setzt zustimmungspflichtige Cookies ein.',
    'cookie_learn_more'                 => 'Details',
    'cookie_reset_consent'              => 'Cookie-Einstellungen zurücksetzen',

    // Footer
    'on'                                => 'auf',

    // Navigation
    'close'                             => 'Schließen',
    'main_navigation'                   => 'Hauptnavigation',
    'menu'                              => 'Menü',
    'nav_close'                         => 'Navigation schließen',
    'nav_open'                          => 'Navigation öffnen',
    'subnav_close'                      => 'Subnavigation schließen',
    'subnav_open'                       => 'Subnavigation öffnen',
    'skip_to_content'                   => 'Zum Inhalt springen',

    // Other
    'no_results'                        => 'Momentan sind keine Einträge vorhanden.',
    'no_script'                         => 'Diese Website funktioniert am besten, wenn JavaScript aktiviert ist.',

    // Pagination
    'next'                              => 'weiter',
    'of'                                => 'von',
    'previous'                          => 'zurück',

    // Social images
    'social_images'                     => 'Generated one social image.|Generated :count social images.',
    'social_images_queue'               => 'Generating one social image in the background.|Generating :count social images in the background.',

    // Widgets
    'widget_assets_title'               => 'Bilder ohne Alternativtext in :container',
    'widget_assets_edit'                => 'Dieses Bild bearbeiten',
    'widget_assets_explanation'         => 'Es ist wichtig, Bilder mit Alternativtexten zu beschreiben. Dies hilft Benutzer:innen von assistiver Technologie, die Inhalte zu erfassen.',
    'widget_assets_count'               => '{0}|{1} :amount Bild benötigt Aufmerksamkeit.|[2,*]Mindestens :amount Bilder benötigen Aufmerksamkeit.',
    'widget_assets_done'                => 'Alle Bilder verfügen über eine Alternativtext.',
];
