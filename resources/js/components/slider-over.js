import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

export default {
	init() {
		gsap.registerPlugin(ScrollTrigger);

        const contentAreaA = document.querySelector(".content-area-a");
        const contentAreaB = document.querySelector(".content-area-b");

        if (!contentAreaA || !contentAreaB) return;

        const createScrollTrigger = () => {
            // Calculate if content-area-a is shorter than viewport
            const viewportHeight = window.innerHeight;
            const contentAreaAHeight = contentAreaA.offsetHeight;
            const isContentShorterThanViewport = contentAreaAHeight < viewportHeight;

            // Main ScrollTrigger animation
            return gsap.timeline({
                scrollTrigger: {
                    trigger: ".content-area-a",
                    // If content is shorter than viewport, start pinning immediately
                    // Otherwise, start when bottom of content-area-a hits bottom of viewport
                    start: isContentShorterThanViewport ? "top top" : "bottom bottom",
                    end: "bottom top", // When bottom of content-area-b hits bottom of viewport
                    endTrigger: ".content-area-b",
                    pin: ".content-area-a", // Pin content-area-a
                    pinSpacing: false, // Don't add extra spacing
                    scrub: true, // Smooth scrubbing tied to scroll position
                    markers: false, // Debug markers (remove in production)
                }
            });
        };

        // Create initial ScrollTrigger
        let scrollTrigger = createScrollTrigger();

        // Refresh ScrollTrigger on window resize to recalculate dimensions
        window.addEventListener('resize', () => {
            scrollTrigger.scrollTrigger.kill();
            scrollTrigger = createScrollTrigger();
        });
	}
}