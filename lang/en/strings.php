<?php
return [
    // Contact form
    'form_error'                        => 'There are errors with your form input. Please fix them and try again.',
    'form_honeypot'                     => 'Fax',
    'form_mail_body_owner'              => 'A contact form has been sent.',
    'form_mail_body_sender'             => 'Thanks for your message. We will contact you as soon as possible.',
    'form_mail_closing'                 => 'Kind regards',
    'form_mail_from'                    => config('app.name'),
    'form_mail_greeting'                => 'Dear',
    'form_mail_logo'                    => 'https://cdn.studio1902.nl/assets/statamic-peak/statamic-peak-logo.png',
    'form_mail_submitted'               => 'Submitted',
    'form_mail_title'                   => 'Contact form',
    'form_mail_url'                     => config('app.url'),
    'form_send'                         => 'Send message',
    'form_subject_received'             => 'Contact form received',
    'form_subject_sent'                 => 'Contact form sent',
    'form_success'                      => 'Thank you, we received your message and will contact you as soon as possible.',

    // <PERSON>ie banner
    'cookie_accept'                     => 'Accept',
    'cookie_ads'                        => 'Ads & tracking',
    'cookie_ads_explanation'            => 'Third party cookies used for advertisement.',
    'cookie_analytics'                  => 'Analytics',
    'cookie_analytics_explanation'      => 'Third party cookies used for analytics.',
    'cookie_explanation'                => 'This site uses cookies that need consent.',
    'cookie_embeds'                     => 'Video embeds',
    'cookie_embeds_explanation'         => 'Third party cookies used for embedded video\'s that can possibly track you.',
    'cookie_embeds_disabled'            => 'Accept video embed cookies to view this video.',
    'cookie_functional'                 => 'Functional',
    'cookie_functional_explanation'     => 'First party cookies needed for the website to function. These can\'t be turned off.',
    'cookie_ignore'                     => 'Ignore',
    'cookie_title'                      => 'Cookies 🍪',
    'cookie_learn_more'                 => 'Learn more',
    'cookie_reset_consent'              => 'Reset cookie consent',

    // Footer
    'on'                                => 'on',

    // Navigation
    'close'                             => 'Close',
    'main_navigation'                   => 'Main navigation',
    'menu'                              => 'Menu',
    'nav_close'                         => 'Close navigation',
    'nav_open'                          => 'Open navigation',
    'subnav_close'                      => 'Close sub navigation',
    'subnav_open'                       => 'Open sub navigation',
    'skip_to_content'                   => 'Skip to content',

    // Other
    'no_results'                        => 'There are currently no posts.',
    'no_script'                         => 'This website requires Javascript for some parts to function properly. Your experience may vary.',

    // Pagination
    'next'                              => 'next',
    'of'                                => 'of',
    'previous'                          => 'previous',

    // Social images
    'social_images'                     => 'Generated one social image.|Generated :count social images.',
    'social_images_queue'               => 'Generating one social image in the background.|Generating :count social images in the background.',

    // Widgets
    'widget_assets_title'               => 'Assets without Alt Text in :container',
    'widget_assets_edit'                => 'Edit this Asset',
    'widget_assets_explanation'         => 'It\'s important to add alt text describing your images. This helps users who depend on assistive technology.',
    'widget_assets_count'               => '{0}You have :amount images that need attention.|{1}You have :amount image that needs attention.|[2,*]You have at least :amount images that need attention.',
    'widget_assets_done'                => 'All assets have an alt text.',
];
