---
id: 55a91be3-d832-430e-9106-3eda56bfd9b0
blueprint: perspectives
title: 'Technology showcase'
article:
  -
    type: paragraph
    content:
      -
        type: text
        text: 'As a web developer, I find nothing more exciting than trying out a new JavaScript framework or build tool that everyone is raving about. However, our world of technology (and the world in general!) is developing at a break-neck pace. With new tools appearing on an almost weekly basis (and with each one better than the last), common sense needs to prevail – at least from a business point of view.'
  -
    type: paragraph
    content:
      -
        type: text
        text: 'These are important decisions for a web development company, and can massively effect the quality and efficiency of your work. A balance needs to be achieved between sticking to tried-and-tested tools and methods, and keeping up to date with the latest trends. Stick too much with the former, and your production process quickly becomes outdated, sub-optimal and you don’t benefit from new technologies. Stray too far into the latter, and you end up never mastering the tools you work with, or find your team in situations where you have little or no trusted community resources to fall back on.'
  -
    type: paragraph
    content:
      -
        type: text
        text: 'We recently decided to move from '
      -
        type: text
        marks:
          -
            type: link
            attrs:
              href: 'https://vuejs.org/'
              rel: noopener
              target: _blank
              title: null
        text: VueJS
      -
        type: text
        text: ' to '
      -
        type: text
        marks:
          -
            type: link
            attrs:
              href: 'https://reactjs.org/'
              rel: noopener
              target: _blank
              title: null
        text: React
      -
        type: text
        text: ' as our JavasScript framework of choice.'
  -
    type: paragraph
    content:
      -
        type: text
        text: 'The reason for this is because we’re looking at improving our mobile app development offering, and at this point we feel '
      -
        type: text
        marks:
          -
            type: link
            attrs:
              href: 'https://facebook.github.io/react-native/'
              rel: noopener
              target: _blank
              title: null
        text: 'React Native'
      -
        type: text
        text: ' has a bigger and more established community compared to the alternative of '
      -
        type: text
        marks:
          -
            type: link
            attrs:
              href: 'https://nativescript-vue.org/'
              rel: noopener
              target: _blank
              title: null
        text: 'Native script + VueJS'
      -
        type: text
        text: '. In an environment where experience and proficiency in your chosen technology is incredibly valuable, splitting web-based and app-based development between two different frameworks just isn’t a good idea.'
  -
    type: paragraph
    content:
      -
        type: text
        text: 'As you can see from the graphs above, even though the uptake of VueJS has been pretty meteoric, NativeScript still has a long way to go compared to React Native.'
  -
    type: paragraph
    content:
      -
        type: text
        text: 'When it comes to '
      -
        type: text
        marks:
          -
            type: link
            attrs:
              href: 'https://developers.google.com/web/tools/setup/setup-buildtools'
              rel: noopener
              target: _blank
              title: null
        text: 'build processes'
      -
        type: text
        text: ' our first choice is '
      -
        type: text
        marks:
          -
            type: link
            attrs:
              href: 'https://webpack.js.org/'
              rel: noopener
              target: _blank
              title: null
        text: webpack
  -
    type: paragraph
    content:
      -
        type: text
        text: 'We’ve used Grunt and Gulp in the past (and still do, when the need arises), but I find Webpack faster and much better suited for modern JavaScript development, even though it’s more complicated to configure.'
  -
    type: paragraph
    content:
      -
        type: text
        text: 'Version control and deployment is another important decision.'
  -
    type: paragraph
    content:
      -
        type: text
        text: 'After giving '
      -
        type: text
        marks:
          -
            type: link
            attrs:
              href: 'https://subversion.apache.org/'
              rel: noopener
              target: _blank
              title: null
        text: SVN
      -
        type: text
        text: ' a fair chance we’ve chosen to stick with '
      -
        type: text
        marks:
          -
            type: link
            attrs:
              href: 'https://git-scm.com/'
              rel: noopener
              target: _blank
              title: null
        text: GIT
      -
        type: text
        text: '. Most of the packages we use are GIT based, it’s more widely used and I personally just like the way it works more (especially with regards to handling branches).'
  -
    type: paragraph
    content:
      -
        type: text
        text: 'When choosing a PHP based framework or CMS, we often have two directions.'
  -
    type: paragraph
    content:
      -
        type: text
        text: 'For websites that require more out-of-the-box functionality and administrator friendliness we tend to gravitate towards '
      -
        type: text
        marks:
          -
            type: link
            attrs:
              href: 'https://wordpress.org/'
              rel: noopener
              target: _blank
              title: null
        text: WordPress
      -
        type: text
        text: '. For me, WordPress is by no means the most developer-friendly CMS out there, but it’s large pool of community resources, third party plugins and user friendly admin dashboard, make it a great choice.'
  -
    type: paragraph
    content:
      -
        type: text
        text: 'For more advanced use cases that require a lot of custom functionality, we use '
      -
        type: text
        marks:
          -
            type: link
            attrs:
              href: 'https://laravel.com/'
              rel: noopener
              target: _blank
              title: null
        text: Laravel
      -
        type: text
        text: '. For the past couple of years it’s been the undisputed king of PHP frameworks and, frankly, we haven’t found anything much better that’s currently available.'
  -
    type: paragraph
    content:
      -
        type: text
        text: 'There are many factors that go into choosing the right tools'
  -
    type: paragraph
    content:
      -
        type: text
        text: '…and although common sense should ultimately prevail, developers should always be experimenting. The more knowledge we can build on in terms of what’s out there and why it’s out there, the easier it will be to make the important decisions when the time comes.'
  -
    type: paragraph
    content:
      -
        type: text
        text: 'Our processes need to be constantly evaluated against what’s available at the moment and where things are heading.'
excerpt: 'As a web developer, I find nothing more exciting than trying out a new JavaScript framework or build tool that everyone is raving about.'
feature_image:
  src: perspectives/technology-showcase.jpg
seo_noindex: false
seo_nofollow: false
seo_canonical_type: entry
sitemap_change_frequency: weekly
sitemap_priority: 0.5
use_dark_header_theme: false
perspectives_categories:
  - development
author: 'Riaan Laubscher'
updated_by: c1804fb6-f691-48f5-be1d-90e69a1966d5
updated_at: 1684924276
seo_title: 'Platinum Seed / Perspectives / Technology showcase'
seo_description: 'When it comes to technology, we lean on our experts.'
---
