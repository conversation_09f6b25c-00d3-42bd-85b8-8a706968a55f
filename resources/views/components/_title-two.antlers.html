---
text: text-dark
slash: fill-accent
---

<h2 class="flex gap-5 items-center text-32 leading-10 font-bold {{ view:text }} {{ class }}">
	<svg width="31" height="68" viewBox="0 0 31 68" fill="none" xmlns="http://www.w3.org/2000/svg">
		<g clip-path="url(#clip0_101_6415)">
			<g style="mix-blend-mode:multiply">
				{{ if view:slash != 'fill-transparent' }}
					<path class="{{ view:slash }}" d="M18.1845 0L0 68H12.8155L31 0L18.1845 0Z" />
				{{ else }}
					<path d="M27.0586 1.50488L10.8232 64.4951H1.94141L18.1768 1.50488L27.0586 1.50488Z" stroke="white" stroke-width="3.00897"/>
				{{ /if }}
			</g>
		</g>
		<defs>
			<clipPath id="clip0_101_6415">
				<rect width="31" height="68" fill="white"/>
			</clipPath>
		</defs>
	</svg>

	{{ slot }}
</h2>