<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Statamic\Events\FormSubmitted;
use Statamic\Events\SubmissionCreated;
use Statamic\Exceptions\SilentFormFailureException;
use Statamic\Exceptions\ValidationException;
use Statamic\Facades\Form;
use Statamic\Facades\Site;
use Statamic\Forms\SendEmails;

class Contact extends Controller
{
     /**
     * Get refreshed CSRF token.
     *
     * @return string
     */
    public function store(Request $request, $form_name)
    {
        $site = Site::default();
		$form = Form::find($form_name->handle);
		$fields = $form->blueprint()->fields();
		$values = $request->all();
		$fields = $fields->addValues($values);
		$submission = $form->makeSubmission();
		$params = collect($request->all())->filter(function ($value, $key) {
			return Str::startsWith($key, '_');
		})->all();

		try {
			// $this->withLocale($site->lang(), function () use ($fields) {
                $fields->validate($this->extraRules($fields));
            // });

			throw_if(Arr::get($values, $form->honeypot()), new SilentFormFailureException());

			$submission->data(
				$fields->addValues($values)->process()->values()
			);

			// If any event listeners return false, we'll do a silent failure.
			// If they want to add validation errors, they can throw an exception.
			throw_if(FormSubmitted::dispatch($submission) === false, new SilentFormFailureException);
		} catch (ValidationException $e) {
			return $this->formFailure($params, $e->errors(), $form->handle());
		} catch (SilentFormFailureException $e) {
			return $this->formSuccess($params, $submission, true);
		}

		if ($form->store()) {
			$submission->save();
		}
		
		SubmissionCreated::dispatch($submission);
		SendEmails::dispatch($submission, $site);

		return response([
			'success' => true,
			'submission_created' => ! false,
			'submission' => $submission->data(),
		]);
    }

	protected function extraRules($fields)
    {
        $assetFieldRules = $fields->all()
            ->filter(function ($field) {
                return $field->fieldtype()->handle() === 'assets';
            })
            ->mapWithKeys(function ($field) {
                return [$field->handle().'.*' => 'file'];
            })
            ->all();

        return $assetFieldRules;
    }

	/**
     * The steps for a successful form submission.
     *
     * Used for actual success and by honeypot.
     *
     * @param  array  $params
     * @param  Submission  $submission
     * @param  bool  $silentFailure
     * @return Response
     */
    function formSuccess($params, $submission, $silentFailure = false)
    {
        if (request()->ajax() || request()->wantsJson()) {
            return response([
                'success' => true,
                'submission_created' => ! $silentFailure,
                'submission' => $submission->data(),
            ]);
        }

        $redirect = Arr::get($params, '_redirect');

        $response = $redirect ? redirect($redirect) : back();

        session()->flash("form.{$submission->form()->handle()}.success", __('Submission successful.'));
        session()->flash("form.{$submission->form()->handle()}.submission_created", ! $silentFailure);
        session()->flash('submission', $submission);

        return $response;
    }
}
