import os
import glob
import csv
import re

def extract_frontmatter(file_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Extract YAML frontmatter between --- delimiters
    frontmatter_match = re.search(r'^---\n(.*?)\n---\n', content, re.DOTALL)
    if not frontmatter_match:
        return {}
    
    frontmatter = {}
    yaml_content = frontmatter_match.group(1)
    current_key = None
    current_list = None
    
    for line in yaml_content.split('\n'):
        line = line.rstrip()
        if not line:
            continue
            
        # Handle list items
        if line.startswith('  - '):
            if current_key and current_list is not None:
                item = line[4:].strip()
                if item.startswith("'"):
                    item = item[1:-1]  # Remove quotes
                current_list.append(item)
            continue
                
        # Handle key-value pairs
        if ':' in line:
            if current_key and current_list is not None:
                frontmatter[current_key] = current_list
                current_list = None
                
            key, value = line.split(':', 1)
            key = key.strip()
            value = value.strip()
            
            # Handle tags specifically
            if key == 'tags':
                current_key = 'tags'
                current_list = []
                # Check if it's an inline list like tags: [tag1, tag2]
                if value.startswith('[') and value.endswith(']'):
                    tags = value[1:-1].split(',')
                    frontmatter['tags'] = [tag.strip().strip("'\"") for tag in tags if tag.strip()]
                    current_list = None
                    current_key = None
            else:
                frontmatter[key] = value.strip("'\"") if value else ''
    
    # Don't forget the last list
    if current_key and current_list is not None:
        frontmatter[current_key] = current_list
    
    return frontmatter

def main():
    case_studies_dir = 'content/collections/case_studies'
    output_file = 'case_studies.csv'
    
    # Get all markdown files
    md_files = glob.glob(os.path.join(case_studies_dir, '*.md'))
    
    # Prepare data
    data = []
    for md_file in md_files:
        fm = extract_frontmatter(md_file)
        if not fm:
            print(f"Warning: Could not parse frontmatter for {md_file}")
            continue
            
        id = fm.get('id', '')
        title = fm.get('title', '')
        hero_title = fm.get('hero_title', '')
        tags = fm.get('tags', [])
        
        # Convert tags list to comma-separated string
        tags_str = ', '.join(tags) if isinstance(tags, list) else tags
        
        data.append({
            'id': id,
            'title': title,
            'hero_title': hero_title,
            'tags': tags_str
        })
    
    # Write to CSV
    with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['id', 'title', 'hero_title', 'tags']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        
        writer.writeheader()
        for row in data:
            writer.writerow(row)
    
    print(f"Successfully generated {output_file} with {len(data)} case studies.")

if __name__ == "__main__":
    main()
