var ja=!1,Wa=!1,bi=[],qa=-1;function Dd(t){Id(t)}function Id(t){bi.includes(t)||bi.push(t),zd()}function Rd(t){let e=bi.indexOf(t);e!==-1&&e>qa&&bi.splice(e,1)}function zd(){!Wa&&!ja&&(ja=!0,queueMicrotask(Fd))}function Fd(){ja=!1,Wa=!0;for(let t=0;t<bi.length;t++)bi[t](),qa=t;bi.length=0,qa=-1,Wa=!1}var fn,$i,cn,Vu,Xa=!0;function $d(t){Xa=!1,t(),Xa=!0}function Bd(t){fn=t.reactive,cn=t.release,$i=e=>t.effect(e,{scheduler:r=>{Xa?Dd(r):r()}}),Vu=t.raw}function Dl(t){$i=t}function Nd(t){let e=()=>{};return[i=>{let n=$i(i);return t._x_effects||(t._x_effects=new Set,t._x_runEffects=()=>{t._x_effects.forEach(s=>s())}),t._x_effects.add(n),e=()=>{n!==void 0&&(t._x_effects.delete(n),cn(n))},n},()=>{e()}]}function Gu(t,e){let r=!0,i,n=$i(()=>{let s=t();JSON.stringify(s),r?i=s:queueMicrotask(()=>{e(s,i),i=s}),r=!1});return()=>cn(n)}var Hu=[],Yu=[],ju=[];function Vd(t){ju.push(t)}function Fo(t,e){typeof e=="function"?(t._x_cleanups||(t._x_cleanups=[]),t._x_cleanups.push(e)):(e=t,Yu.push(e))}function Wu(t){Hu.push(t)}function qu(t,e,r){t._x_attributeCleanups||(t._x_attributeCleanups={}),t._x_attributeCleanups[e]||(t._x_attributeCleanups[e]=[]),t._x_attributeCleanups[e].push(r)}function Xu(t,e){t._x_attributeCleanups&&Object.entries(t._x_attributeCleanups).forEach(([r,i])=>{(e===void 0||e.includes(r))&&(i.forEach(n=>n()),delete t._x_attributeCleanups[r])})}function Gd(t){var e,r;for((e=t._x_effects)==null||e.forEach(Rd);(r=t._x_cleanups)!=null&&r.length;)t._x_cleanups.pop()()}var $o=new MutationObserver(Go),Bo=!1;function No(){$o.observe(document,{subtree:!0,childList:!0,attributes:!0,attributeOldValue:!0}),Bo=!0}function Uu(){Hd(),$o.disconnect(),Bo=!1}var mn=[];function Hd(){let t=$o.takeRecords();mn.push(()=>t.length>0&&Go(t));let e=mn.length;queueMicrotask(()=>{if(mn.length===e)for(;mn.length>0;)mn.shift()()})}function ke(t){if(!Bo)return t();Uu();let e=t();return No(),e}var Vo=!1,Hs=[];function Yd(){Vo=!0}function jd(){Vo=!1,Go(Hs),Hs=[]}function Go(t){if(Vo){Hs=Hs.concat(t);return}let e=[],r=new Set,i=new Map,n=new Map;for(let s=0;s<t.length;s++)if(!t[s].target._x_ignoreMutationObserver&&(t[s].type==="childList"&&(t[s].removedNodes.forEach(a=>{a.nodeType===1&&a._x_marker&&r.add(a)}),t[s].addedNodes.forEach(a=>{if(a.nodeType===1){if(r.has(a)){r.delete(a);return}a._x_marker||e.push(a)}})),t[s].type==="attributes")){let a=t[s].target,o=t[s].attributeName,l=t[s].oldValue,u=()=>{i.has(a)||i.set(a,[]),i.get(a).push({name:o,value:a.getAttribute(o)})},f=()=>{n.has(a)||n.set(a,[]),n.get(a).push(o)};a.hasAttribute(o)&&l===null?u():a.hasAttribute(o)?(f(),u()):f()}n.forEach((s,a)=>{Xu(a,s)}),i.forEach((s,a)=>{Hu.forEach(o=>o(a,s))});for(let s of r)e.some(a=>a.contains(s))||Yu.forEach(a=>a(s));for(let s of e)s.isConnected&&ju.forEach(a=>a(s));e=null,r=null,i=null,n=null}function Ku(t){return ns(en(t))}function is(t,e,r){return t._x_dataStack=[e,...en(r||t)],()=>{t._x_dataStack=t._x_dataStack.filter(i=>i!==e)}}function en(t){return t._x_dataStack?t._x_dataStack:typeof ShadowRoot=="function"&&t instanceof ShadowRoot?en(t.host):t.parentNode?en(t.parentNode):[]}function ns(t){return new Proxy({objects:t},Wd)}var Wd={ownKeys({objects:t}){return Array.from(new Set(t.flatMap(e=>Object.keys(e))))},has({objects:t},e){return e==Symbol.unscopables?!1:t.some(r=>Object.prototype.hasOwnProperty.call(r,e)||Reflect.has(r,e))},get({objects:t},e,r){return e=="toJSON"?qd:Reflect.get(t.find(i=>Reflect.has(i,e))||{},e,r)},set({objects:t},e,r,i){const n=t.find(a=>Object.prototype.hasOwnProperty.call(a,e))||t[t.length-1],s=Object.getOwnPropertyDescriptor(n,e);return s!=null&&s.set&&(s!=null&&s.get)?s.set.call(i,r)||!0:Reflect.set(n,e,r)}};function qd(){return Reflect.ownKeys(this).reduce((e,r)=>(e[r]=Reflect.get(this,r),e),{})}function Ju(t){let e=i=>typeof i=="object"&&!Array.isArray(i)&&i!==null,r=(i,n="")=>{Object.entries(Object.getOwnPropertyDescriptors(i)).forEach(([s,{value:a,enumerable:o}])=>{if(o===!1||a===void 0||typeof a=="object"&&a!==null&&a.__v_skip)return;let l=n===""?s:`${n}.${s}`;typeof a=="object"&&a!==null&&a._x_interceptor?i[s]=a.initialize(t,l,s):e(a)&&a!==i&&!(a instanceof Element)&&r(a,l)})};return r(t)}function Zu(t,e=()=>{}){let r={initialValue:void 0,_x_interceptor:!0,initialize(i,n,s){return t(this.initialValue,()=>Xd(i,n),a=>Ua(i,n,a),n,s)}};return e(r),i=>{if(typeof i=="object"&&i!==null&&i._x_interceptor){let n=r.initialize.bind(r);r.initialize=(s,a,o)=>{let l=i.initialize(s,a,o);return r.initialValue=l,n(s,a,o)}}else r.initialValue=i;return r}}function Xd(t,e){return e.split(".").reduce((r,i)=>r[i],t)}function Ua(t,e,r){if(typeof e=="string"&&(e=e.split(".")),e.length===1)t[e[0]]=r;else{if(e.length===0)throw error;return t[e[0]]||(t[e[0]]={}),Ua(t[e[0]],e.slice(1),r)}}var Qu={};function gr(t,e){Qu[t]=e}function Ka(t,e){let r=Ud(e);return Object.entries(Qu).forEach(([i,n])=>{Object.defineProperty(t,`$${i}`,{get(){return n(e,r)},enumerable:!1})}),t}function Ud(t){let[e,r]=af(t),i={interceptor:Zu,...e};return Fo(t,r),i}function Kd(t,e,r,...i){try{return r(...i)}catch(n){qn(n,t,e)}}function qn(t,e,r=void 0){t=Object.assign(t??{message:"No error message given."},{el:e,expression:r}),console.warn(`Alpine Expression Error: ${t.message}

${r?'Expression: "'+r+`"

`:""}`,e),setTimeout(()=>{throw t},0)}var ks=!0;function ef(t){let e=ks;ks=!1;let r=t();return ks=e,r}function Si(t,e,r={}){let i;return Ot(t,e)(n=>i=n,r),i}function Ot(...t){return tf(...t)}var tf=rf;function Jd(t){tf=t}function rf(t,e){let r={};Ka(r,t);let i=[r,...en(t)],n=typeof e=="function"?Zd(i,e):ep(i,e,t);return Kd.bind(null,t,e,n)}function Zd(t,e){return(r=()=>{},{scope:i={},params:n=[]}={})=>{let s=e.apply(ns([i,...t]),n);Ys(r,s)}}var ya={};function Qd(t,e){if(ya[t])return ya[t];let r=Object.getPrototypeOf(async function(){}).constructor,i=/^[\n\s]*if.*\(.*\)/.test(t.trim())||/^(let|const)\s/.test(t.trim())?`(async()=>{ ${t} })()`:t,s=(()=>{try{let a=new r(["__self","scope"],`with (scope) { __self.result = ${i} }; __self.finished = true; return __self.result;`);return Object.defineProperty(a,"name",{value:`[Alpine] ${t}`}),a}catch(a){return qn(a,e,t),Promise.resolve()}})();return ya[t]=s,s}function ep(t,e,r){let i=Qd(e,r);return(n=()=>{},{scope:s={},params:a=[]}={})=>{i.result=void 0,i.finished=!1;let o=ns([s,...t]);if(typeof i=="function"){let l=i(i,o).catch(u=>qn(u,r,e));i.finished?(Ys(n,i.result,o,a,r),i.result=void 0):l.then(u=>{Ys(n,u,o,a,r)}).catch(u=>qn(u,r,e)).finally(()=>i.result=void 0)}}}function Ys(t,e,r,i,n){if(ks&&typeof e=="function"){let s=e.apply(r,i);s instanceof Promise?s.then(a=>Ys(t,a,r,i)).catch(a=>qn(a,n,e)):t(s)}else typeof e=="object"&&e instanceof Promise?e.then(s=>t(s)):t(e)}var Ho="x-";function dn(t=""){return Ho+t}function tp(t){Ho=t}var js={};function Ye(t,e){return js[t]=e,{before(r){if(!js[r]){console.warn(String.raw`Cannot find directive \`${r}\`. \`${t}\` will use the default order of execution`);return}const i=vi.indexOf(r);vi.splice(i>=0?i:vi.indexOf("DEFAULT"),0,t)}}}function rp(t){return Object.keys(js).includes(t)}function Yo(t,e,r){if(e=Array.from(e),t._x_virtualDirectives){let s=Object.entries(t._x_virtualDirectives).map(([o,l])=>({name:o,value:l})),a=nf(s);s=s.map(o=>a.find(l=>l.name===o.name)?{name:`x-bind:${o.name}`,value:`"${o.value}"`}:o),e=e.concat(s)}let i={};return e.map(uf((s,a)=>i[s]=a)).filter(cf).map(sp(i,r)).sort(ap).map(s=>np(t,s))}function nf(t){return Array.from(t).map(uf()).filter(e=>!cf(e))}var Ja=!1,Tn=new Map,sf=Symbol();function ip(t){Ja=!0;let e=Symbol();sf=e,Tn.set(e,[]);let r=()=>{for(;Tn.get(e).length;)Tn.get(e).shift()();Tn.delete(e)},i=()=>{Ja=!1,r()};t(r),i()}function af(t){let e=[],r=o=>e.push(o),[i,n]=Nd(t);return e.push(n),[{Alpine:ss,effect:i,cleanup:r,evaluateLater:Ot.bind(Ot,t),evaluate:Si.bind(Si,t)},()=>e.forEach(o=>o())]}function np(t,e){let r=()=>{},i=js[e.type]||r,[n,s]=af(t);qu(t,e.original,s);let a=()=>{t._x_ignore||t._x_ignoreSelf||(i.inline&&i.inline(t,e,n),i=i.bind(i,t,e,n),Ja?Tn.get(sf).push(i):i())};return a.runCleanups=s,a}var of=(t,e)=>({name:r,value:i})=>(r.startsWith(t)&&(r=r.replace(t,e)),{name:r,value:i}),lf=t=>t;function uf(t=()=>{}){return({name:e,value:r})=>{let{name:i,value:n}=ff.reduce((s,a)=>a(s),{name:e,value:r});return i!==e&&t(i,e),{name:i,value:n}}}var ff=[];function jo(t){ff.push(t)}function cf({name:t}){return df().test(t)}var df=()=>new RegExp(`^${Ho}([^:^.]+)\\b`);function sp(t,e){return({name:r,value:i})=>{let n=r.match(df()),s=r.match(/:([a-zA-Z0-9\-_:]+)/),a=r.match(/\.[^.\]]+(?=[^\]]*$)/g)||[],o=e||t[r]||r;return{type:n?n[1]:null,value:s?s[1]:null,modifiers:a.map(l=>l.replace(".","")),expression:i,original:o}}}var Za="DEFAULT",vi=["ignore","ref","data","id","anchor","bind","init","for","model","modelable","transition","show","if",Za,"teleport"];function ap(t,e){let r=vi.indexOf(t.type)===-1?Za:t.type,i=vi.indexOf(e.type)===-1?Za:e.type;return vi.indexOf(r)-vi.indexOf(i)}function Dn(t,e,r={}){t.dispatchEvent(new CustomEvent(e,{detail:r,bubbles:!0,composed:!0,cancelable:!0}))}function Li(t,e){if(typeof ShadowRoot=="function"&&t instanceof ShadowRoot){Array.from(t.children).forEach(n=>Li(n,e));return}let r=!1;if(e(t,()=>r=!0),r)return;let i=t.firstElementChild;for(;i;)Li(i,e),i=i.nextElementSibling}function ir(t,...e){console.warn(`Alpine Warning: ${t}`,...e)}var Il=!1;function op(){Il&&ir("Alpine has already been initialized on this page. Calling Alpine.start() more than once can cause problems."),Il=!0,document.body||ir("Unable to initialize. Trying to load Alpine before `<body>` is available. Did you forget to add `defer` in Alpine's `<script>` tag?"),Dn(document,"alpine:init"),Dn(document,"alpine:initializing"),No(),Vd(e=>$r(e,Li)),Fo(e=>hn(e)),Wu((e,r)=>{Yo(e,r).forEach(i=>i())});let t=e=>!oa(e.parentElement,!0);Array.from(document.querySelectorAll(gf().join(","))).filter(t).forEach(e=>{$r(e)}),Dn(document,"alpine:initialized"),setTimeout(()=>{cp()})}var Wo=[],pf=[];function hf(){return Wo.map(t=>t())}function gf(){return Wo.concat(pf).map(t=>t())}function _f(t){Wo.push(t)}function mf(t){pf.push(t)}function oa(t,e=!1){return pn(t,r=>{if((e?gf():hf()).some(n=>r.matches(n)))return!0})}function pn(t,e){if(t){if(e(t))return t;if(t._x_teleportBack&&(t=t._x_teleportBack),!!t.parentElement)return pn(t.parentElement,e)}}function lp(t){return hf().some(e=>t.matches(e))}var vf=[];function up(t){vf.push(t)}var fp=1;function $r(t,e=Li,r=()=>{}){pn(t,i=>i._x_ignore)||ip(()=>{e(t,(i,n)=>{i._x_marker||(r(i,n),vf.forEach(s=>s(i,n)),Yo(i,i.attributes).forEach(s=>s()),i._x_ignore||(i._x_marker=fp++),i._x_ignore&&n())})})}function hn(t,e=Li){e(t,r=>{Gd(r),Xu(r),delete r._x_marker})}function cp(){[["ui","dialog",["[x-dialog], [x-popover]"]],["anchor","anchor",["[x-anchor]"]],["sort","sort",["[x-sort]"]]].forEach(([e,r,i])=>{rp(r)||i.some(n=>{if(document.querySelector(n))return ir(`found "${n}", but missing ${e} plugin`),!0})})}var Qa=[],qo=!1;function Xo(t=()=>{}){return queueMicrotask(()=>{qo||setTimeout(()=>{eo()})}),new Promise(e=>{Qa.push(()=>{t(),e()})})}function eo(){for(qo=!1;Qa.length;)Qa.shift()()}function dp(){qo=!0}function Uo(t,e){return Array.isArray(e)?Rl(t,e.join(" ")):typeof e=="object"&&e!==null?pp(t,e):typeof e=="function"?Uo(t,e()):Rl(t,e)}function Rl(t,e){let r=n=>n.split(" ").filter(s=>!t.classList.contains(s)).filter(Boolean),i=n=>(t.classList.add(...n),()=>{t.classList.remove(...n)});return e=e===!0?e="":e||"",i(r(e))}function pp(t,e){let r=o=>o.split(" ").filter(Boolean),i=Object.entries(e).flatMap(([o,l])=>l?r(o):!1).filter(Boolean),n=Object.entries(e).flatMap(([o,l])=>l?!1:r(o)).filter(Boolean),s=[],a=[];return n.forEach(o=>{t.classList.contains(o)&&(t.classList.remove(o),a.push(o))}),i.forEach(o=>{t.classList.contains(o)||(t.classList.add(o),s.push(o))}),()=>{a.forEach(o=>t.classList.add(o)),s.forEach(o=>t.classList.remove(o))}}function la(t,e){return typeof e=="object"&&e!==null?hp(t,e):gp(t,e)}function hp(t,e){let r={};return Object.entries(e).forEach(([i,n])=>{r[i]=t.style[i],i.startsWith("--")||(i=_p(i)),t.style.setProperty(i,n)}),setTimeout(()=>{t.style.length===0&&t.removeAttribute("style")}),()=>{la(t,r)}}function gp(t,e){let r=t.getAttribute("style",e);return t.setAttribute("style",e),()=>{t.setAttribute("style",r||"")}}function _p(t){return t.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase()}function to(t,e=()=>{}){let r=!1;return function(){r?e.apply(this,arguments):(r=!0,t.apply(this,arguments))}}Ye("transition",(t,{value:e,modifiers:r,expression:i},{evaluate:n})=>{typeof i=="function"&&(i=n(i)),i!==!1&&(!i||typeof i=="boolean"?vp(t,r,e):mp(t,i,e))});function mp(t,e,r){yf(t,Uo,""),{enter:n=>{t._x_transition.enter.during=n},"enter-start":n=>{t._x_transition.enter.start=n},"enter-end":n=>{t._x_transition.enter.end=n},leave:n=>{t._x_transition.leave.during=n},"leave-start":n=>{t._x_transition.leave.start=n},"leave-end":n=>{t._x_transition.leave.end=n}}[r](e)}function vp(t,e,r){yf(t,la);let i=!e.includes("in")&&!e.includes("out")&&!r,n=i||e.includes("in")||["enter"].includes(r),s=i||e.includes("out")||["leave"].includes(r);e.includes("in")&&!i&&(e=e.filter((g,v)=>v<e.indexOf("out"))),e.includes("out")&&!i&&(e=e.filter((g,v)=>v>e.indexOf("out")));let a=!e.includes("opacity")&&!e.includes("scale"),o=a||e.includes("opacity"),l=a||e.includes("scale"),u=o?0:1,f=l?vn(e,"scale",95)/100:1,c=vn(e,"delay",0)/1e3,p=vn(e,"origin","center"),d="opacity, transform",_=vn(e,"duration",150)/1e3,h=vn(e,"duration",75)/1e3,m="cubic-bezier(0.4, 0.0, 0.2, 1)";n&&(t._x_transition.enter.during={transformOrigin:p,transitionDelay:`${c}s`,transitionProperty:d,transitionDuration:`${_}s`,transitionTimingFunction:m},t._x_transition.enter.start={opacity:u,transform:`scale(${f})`},t._x_transition.enter.end={opacity:1,transform:"scale(1)"}),s&&(t._x_transition.leave.during={transformOrigin:p,transitionDelay:`${c}s`,transitionProperty:d,transitionDuration:`${h}s`,transitionTimingFunction:m},t._x_transition.leave.start={opacity:1,transform:"scale(1)"},t._x_transition.leave.end={opacity:u,transform:`scale(${f})`})}function yf(t,e,r={}){t._x_transition||(t._x_transition={enter:{during:r,start:r,end:r},leave:{during:r,start:r,end:r},in(i=()=>{},n=()=>{}){ro(t,e,{during:this.enter.during,start:this.enter.start,end:this.enter.end},i,n)},out(i=()=>{},n=()=>{}){ro(t,e,{during:this.leave.during,start:this.leave.start,end:this.leave.end},i,n)}})}window.Element.prototype._x_toggleAndCascadeWithTransitions=function(t,e,r,i){const n=document.visibilityState==="visible"?requestAnimationFrame:setTimeout;let s=()=>n(r);if(e){t._x_transition&&(t._x_transition.enter||t._x_transition.leave)?t._x_transition.enter&&(Object.entries(t._x_transition.enter.during).length||Object.entries(t._x_transition.enter.start).length||Object.entries(t._x_transition.enter.end).length)?t._x_transition.in(r):s():t._x_transition?t._x_transition.in(r):s();return}t._x_hidePromise=t._x_transition?new Promise((a,o)=>{t._x_transition.out(()=>{},()=>a(i)),t._x_transitioning&&t._x_transitioning.beforeCancel(()=>o({isFromCancelledTransition:!0}))}):Promise.resolve(i),queueMicrotask(()=>{let a=xf(t);a?(a._x_hideChildren||(a._x_hideChildren=[]),a._x_hideChildren.push(t)):n(()=>{let o=l=>{let u=Promise.all([l._x_hidePromise,...(l._x_hideChildren||[]).map(o)]).then(([f])=>f==null?void 0:f());return delete l._x_hidePromise,delete l._x_hideChildren,u};o(t).catch(l=>{if(!l.isFromCancelledTransition)throw l})})})};function xf(t){let e=t.parentNode;if(e)return e._x_hidePromise?e:xf(e)}function ro(t,e,{during:r,start:i,end:n}={},s=()=>{},a=()=>{}){if(t._x_transitioning&&t._x_transitioning.cancel(),Object.keys(r).length===0&&Object.keys(i).length===0&&Object.keys(n).length===0){s(),a();return}let o,l,u;yp(t,{start(){o=e(t,i)},during(){l=e(t,r)},before:s,end(){o(),u=e(t,n)},after:a,cleanup(){l(),u()}})}function yp(t,e){let r,i,n,s=to(()=>{ke(()=>{r=!0,i||e.before(),n||(e.end(),eo()),e.after(),t.isConnected&&e.cleanup(),delete t._x_transitioning})});t._x_transitioning={beforeCancels:[],beforeCancel(a){this.beforeCancels.push(a)},cancel:to(function(){for(;this.beforeCancels.length;)this.beforeCancels.shift()();s()}),finish:s},ke(()=>{e.start(),e.during()}),dp(),requestAnimationFrame(()=>{if(r)return;let a=Number(getComputedStyle(t).transitionDuration.replace(/,.*/,"").replace("s",""))*1e3,o=Number(getComputedStyle(t).transitionDelay.replace(/,.*/,"").replace("s",""))*1e3;a===0&&(a=Number(getComputedStyle(t).animationDuration.replace("s",""))*1e3),ke(()=>{e.before()}),i=!0,requestAnimationFrame(()=>{r||(ke(()=>{e.end()}),eo(),setTimeout(t._x_transitioning.finish,a+o),n=!0)})})}function vn(t,e,r){if(t.indexOf(e)===-1)return r;const i=t[t.indexOf(e)+1];if(!i||e==="scale"&&isNaN(i))return r;if(e==="duration"||e==="delay"){let n=i.match(/([0-9]+)ms/);if(n)return n[1]}return e==="origin"&&["top","right","left","center","bottom"].includes(t[t.indexOf(e)+2])?[i,t[t.indexOf(e)+2]].join(" "):i}var ri=!1;function li(t,e=()=>{}){return(...r)=>ri?e(...r):t(...r)}function xp(t){return(...e)=>ri&&t(...e)}var bf=[];function ua(t){bf.push(t)}function bp(t,e){bf.forEach(r=>r(t,e)),ri=!0,Sf(()=>{$r(e,(r,i)=>{i(r,()=>{})})}),ri=!1}var io=!1;function Sp(t,e){e._x_dataStack||(e._x_dataStack=t._x_dataStack),ri=!0,io=!0,Sf(()=>{wp(e)}),ri=!1,io=!1}function wp(t){let e=!1;$r(t,(i,n)=>{Li(i,(s,a)=>{if(e&&lp(s))return a();e=!0,n(s,a)})})}function Sf(t){let e=$i;Dl((r,i)=>{let n=e(r);return cn(n),()=>{}}),t(),Dl(e)}function wf(t,e,r,i=[]){switch(t._x_bindings||(t._x_bindings=fn({})),t._x_bindings[e]=r,e=i.includes("camel")?kp(e):e,e){case"value":Tp(t,r);break;case"style":Ep(t,r);break;case"class":Cp(t,r);break;case"selected":case"checked":Pp(t,e,r);break;default:Tf(t,e,r);break}}function Tp(t,e){if(Pf(t))t.attributes.value===void 0&&(t.value=e),window.fromModel&&(typeof e=="boolean"?t.checked=Ls(t.value)===e:t.checked=zl(t.value,e));else if(Ko(t))Number.isInteger(e)?t.value=e:!Array.isArray(e)&&typeof e!="boolean"&&![null,void 0].includes(e)?t.value=String(e):Array.isArray(e)?t.checked=e.some(r=>zl(r,t.value)):t.checked=!!e;else if(t.tagName==="SELECT")Ap(t,e);else{if(t.value===e)return;t.value=e===void 0?"":e}}function Cp(t,e){t._x_undoAddedClasses&&t._x_undoAddedClasses(),t._x_undoAddedClasses=Uo(t,e)}function Ep(t,e){t._x_undoAddedStyles&&t._x_undoAddedStyles(),t._x_undoAddedStyles=la(t,e)}function Pp(t,e,r){Tf(t,e,r),Op(t,e,r)}function Tf(t,e,r){[null,void 0,!1].includes(r)&&Dp(e)?t.removeAttribute(e):(Cf(e)&&(r=e),Mp(t,e,r))}function Mp(t,e,r){t.getAttribute(e)!=r&&t.setAttribute(e,r)}function Op(t,e,r){t[e]!==r&&(t[e]=r)}function Ap(t,e){const r=[].concat(e).map(i=>i+"");Array.from(t.options).forEach(i=>{i.selected=r.includes(i.value)})}function kp(t){return t.toLowerCase().replace(/-(\w)/g,(e,r)=>r.toUpperCase())}function zl(t,e){return t==e}function Ls(t){return[1,"1","true","on","yes",!0].includes(t)?!0:[0,"0","false","off","no",!1].includes(t)?!1:t?!!t:null}var Lp=new Set(["allowfullscreen","async","autofocus","autoplay","checked","controls","default","defer","disabled","formnovalidate","inert","ismap","itemscope","loop","multiple","muted","nomodule","novalidate","open","playsinline","readonly","required","reversed","selected","shadowrootclonable","shadowrootdelegatesfocus","shadowrootserializable"]);function Cf(t){return Lp.has(t)}function Dp(t){return!["aria-pressed","aria-checked","aria-expanded","aria-selected"].includes(t)}function Ip(t,e,r){return t._x_bindings&&t._x_bindings[e]!==void 0?t._x_bindings[e]:Ef(t,e,r)}function Rp(t,e,r,i=!0){if(t._x_bindings&&t._x_bindings[e]!==void 0)return t._x_bindings[e];if(t._x_inlineBindings&&t._x_inlineBindings[e]!==void 0){let n=t._x_inlineBindings[e];return n.extract=i,ef(()=>Si(t,n.expression))}return Ef(t,e,r)}function Ef(t,e,r){let i=t.getAttribute(e);return i===null?typeof r=="function"?r():r:i===""?!0:Cf(e)?!![e,"true"].includes(i):i}function Ko(t){return t.type==="checkbox"||t.localName==="ui-checkbox"||t.localName==="ui-switch"}function Pf(t){return t.type==="radio"||t.localName==="ui-radio"}function Mf(t,e){var r;return function(){var i=this,n=arguments,s=function(){r=null,t.apply(i,n)};clearTimeout(r),r=setTimeout(s,e)}}function Of(t,e){let r;return function(){let i=this,n=arguments;r||(t.apply(i,n),r=!0,setTimeout(()=>r=!1,e))}}function Af({get:t,set:e},{get:r,set:i}){let n=!0,s,a=$i(()=>{let o=t(),l=r();if(n)i(xa(o)),n=!1;else{let u=JSON.stringify(o),f=JSON.stringify(l);u!==s?i(xa(o)):u!==f&&e(xa(l))}s=JSON.stringify(t()),JSON.stringify(r())});return()=>{cn(a)}}function xa(t){return typeof t=="object"?JSON.parse(JSON.stringify(t)):t}function zp(t){(Array.isArray(t)?t:[t]).forEach(r=>r(ss))}var pi={},Fl=!1;function Fp(t,e){if(Fl||(pi=fn(pi),Fl=!0),e===void 0)return pi[t];pi[t]=e,Ju(pi[t]),typeof e=="object"&&e!==null&&e.hasOwnProperty("init")&&typeof e.init=="function"&&pi[t].init()}function $p(){return pi}var kf={};function Bp(t,e){let r=typeof e!="function"?()=>e:e;return t instanceof Element?Lf(t,r()):(kf[t]=r,()=>{})}function Np(t){return Object.entries(kf).forEach(([e,r])=>{Object.defineProperty(t,e,{get(){return(...i)=>r(...i)}})}),t}function Lf(t,e,r){let i=[];for(;i.length;)i.pop()();let n=Object.entries(e).map(([a,o])=>({name:a,value:o})),s=nf(n);return n=n.map(a=>s.find(o=>o.name===a.name)?{name:`x-bind:${a.name}`,value:`"${a.value}"`}:a),Yo(t,n,r).map(a=>{i.push(a.runCleanups),a()}),()=>{for(;i.length;)i.pop()()}}var Df={};function Vp(t,e){Df[t]=e}function Gp(t,e){return Object.entries(Df).forEach(([r,i])=>{Object.defineProperty(t,r,{get(){return(...n)=>i.bind(e)(...n)},enumerable:!1})}),t}var Hp={get reactive(){return fn},get release(){return cn},get effect(){return $i},get raw(){return Vu},version:"3.14.9",flushAndStopDeferringMutations:jd,dontAutoEvaluateFunctions:ef,disableEffectScheduling:$d,startObservingMutations:No,stopObservingMutations:Uu,setReactivityEngine:Bd,onAttributeRemoved:qu,onAttributesAdded:Wu,closestDataStack:en,skipDuringClone:li,onlyDuringClone:xp,addRootSelector:_f,addInitSelector:mf,interceptClone:ua,addScopeToNode:is,deferMutations:Yd,mapAttributes:jo,evaluateLater:Ot,interceptInit:up,setEvaluator:Jd,mergeProxies:ns,extractProp:Rp,findClosest:pn,onElRemoved:Fo,closestRoot:oa,destroyTree:hn,interceptor:Zu,transition:ro,setStyles:la,mutateDom:ke,directive:Ye,entangle:Af,throttle:Of,debounce:Mf,evaluate:Si,initTree:$r,nextTick:Xo,prefixed:dn,prefix:tp,plugin:zp,magic:gr,store:Fp,start:op,clone:Sp,cloneNode:bp,bound:Ip,$data:Ku,watch:Gu,walk:Li,data:Vp,bind:Bp},ss=Hp;function Yp(t,e){const r=Object.create(null),i=t.split(",");for(let n=0;n<i.length;n++)r[i[n]]=!0;return e?n=>!!r[n.toLowerCase()]:n=>!!r[n]}var jp=Object.freeze({}),Wp=Object.prototype.hasOwnProperty,fa=(t,e)=>Wp.call(t,e),wi=Array.isArray,In=t=>If(t)==="[object Map]",qp=t=>typeof t=="string",Jo=t=>typeof t=="symbol",ca=t=>t!==null&&typeof t=="object",Xp=Object.prototype.toString,If=t=>Xp.call(t),Rf=t=>If(t).slice(8,-1),Zo=t=>qp(t)&&t!=="NaN"&&t[0]!=="-"&&""+parseInt(t,10)===t,Up=t=>{const e=Object.create(null);return r=>e[r]||(e[r]=t(r))},Kp=Up(t=>t.charAt(0).toUpperCase()+t.slice(1)),zf=(t,e)=>t!==e&&(t===t||e===e),no=new WeakMap,yn=[],xr,Ti=Symbol("iterate"),so=Symbol("Map key iterate");function Jp(t){return t&&t._isEffect===!0}function Zp(t,e=jp){Jp(t)&&(t=t.raw);const r=th(t,e);return e.lazy||r(),r}function Qp(t){t.active&&(Ff(t),t.options.onStop&&t.options.onStop(),t.active=!1)}var eh=0;function th(t,e){const r=function(){if(!r.active)return t();if(!yn.includes(r)){Ff(r);try{return ih(),yn.push(r),xr=r,t()}finally{yn.pop(),$f(),xr=yn[yn.length-1]}}};return r.id=eh++,r.allowRecurse=!!e.allowRecurse,r._isEffect=!0,r.active=!0,r.raw=t,r.deps=[],r.options=e,r}function Ff(t){const{deps:e}=t;if(e.length){for(let r=0;r<e.length;r++)e[r].delete(t);e.length=0}}var tn=!0,Qo=[];function rh(){Qo.push(tn),tn=!1}function ih(){Qo.push(tn),tn=!0}function $f(){const t=Qo.pop();tn=t===void 0?!0:t}function hr(t,e,r){if(!tn||xr===void 0)return;let i=no.get(t);i||no.set(t,i=new Map);let n=i.get(r);n||i.set(r,n=new Set),n.has(xr)||(n.add(xr),xr.deps.push(n),xr.options.onTrack&&xr.options.onTrack({effect:xr,target:t,type:e,key:r}))}function ii(t,e,r,i,n,s){const a=no.get(t);if(!a)return;const o=new Set,l=f=>{f&&f.forEach(c=>{(c!==xr||c.allowRecurse)&&o.add(c)})};if(e==="clear")a.forEach(l);else if(r==="length"&&wi(t))a.forEach((f,c)=>{(c==="length"||c>=i)&&l(f)});else switch(r!==void 0&&l(a.get(r)),e){case"add":wi(t)?Zo(r)&&l(a.get("length")):(l(a.get(Ti)),In(t)&&l(a.get(so)));break;case"delete":wi(t)||(l(a.get(Ti)),In(t)&&l(a.get(so)));break;case"set":In(t)&&l(a.get(Ti));break}const u=f=>{f.options.onTrigger&&f.options.onTrigger({effect:f,target:t,key:r,type:e,newValue:i,oldValue:n,oldTarget:s}),f.options.scheduler?f.options.scheduler(f):f()};o.forEach(u)}var nh=Yp("__proto__,__v_isRef,__isVue"),Bf=new Set(Object.getOwnPropertyNames(Symbol).map(t=>Symbol[t]).filter(Jo)),sh=Nf(),ah=Nf(!0),$l=oh();function oh(){const t={};return["includes","indexOf","lastIndexOf"].forEach(e=>{t[e]=function(...r){const i=Se(this);for(let s=0,a=this.length;s<a;s++)hr(i,"get",s+"");const n=i[e](...r);return n===-1||n===!1?i[e](...r.map(Se)):n}}),["push","pop","shift","unshift","splice"].forEach(e=>{t[e]=function(...r){rh();const i=Se(this)[e].apply(this,r);return $f(),i}}),t}function Nf(t=!1,e=!1){return function(i,n,s){if(n==="__v_isReactive")return!t;if(n==="__v_isReadonly")return t;if(n==="__v_raw"&&s===(t?e?wh:Yf:e?Sh:Hf).get(i))return i;const a=wi(i);if(!t&&a&&fa($l,n))return Reflect.get($l,n,s);const o=Reflect.get(i,n,s);return(Jo(n)?Bf.has(n):nh(n))||(t||hr(i,"get",n),e)?o:ao(o)?!a||!Zo(n)?o.value:o:ca(o)?t?jf(o):il(o):o}}var lh=uh();function uh(t=!1){return function(r,i,n,s){let a=r[i];if(!t&&(n=Se(n),a=Se(a),!wi(r)&&ao(a)&&!ao(n)))return a.value=n,!0;const o=wi(r)&&Zo(i)?Number(i)<r.length:fa(r,i),l=Reflect.set(r,i,n,s);return r===Se(s)&&(o?zf(n,a)&&ii(r,"set",i,n,a):ii(r,"add",i,n)),l}}function fh(t,e){const r=fa(t,e),i=t[e],n=Reflect.deleteProperty(t,e);return n&&r&&ii(t,"delete",e,void 0,i),n}function ch(t,e){const r=Reflect.has(t,e);return(!Jo(e)||!Bf.has(e))&&hr(t,"has",e),r}function dh(t){return hr(t,"iterate",wi(t)?"length":Ti),Reflect.ownKeys(t)}var ph={get:sh,set:lh,deleteProperty:fh,has:ch,ownKeys:dh},hh={get:ah,set(t,e){return console.warn(`Set operation on key "${String(e)}" failed: target is readonly.`,t),!0},deleteProperty(t,e){return console.warn(`Delete operation on key "${String(e)}" failed: target is readonly.`,t),!0}},el=t=>ca(t)?il(t):t,tl=t=>ca(t)?jf(t):t,rl=t=>t,da=t=>Reflect.getPrototypeOf(t);function us(t,e,r=!1,i=!1){t=t.__v_raw;const n=Se(t),s=Se(e);e!==s&&!r&&hr(n,"get",e),!r&&hr(n,"get",s);const{has:a}=da(n),o=i?rl:r?tl:el;if(a.call(n,e))return o(t.get(e));if(a.call(n,s))return o(t.get(s));t!==n&&t.get(e)}function fs(t,e=!1){const r=this.__v_raw,i=Se(r),n=Se(t);return t!==n&&!e&&hr(i,"has",t),!e&&hr(i,"has",n),t===n?r.has(t):r.has(t)||r.has(n)}function cs(t,e=!1){return t=t.__v_raw,!e&&hr(Se(t),"iterate",Ti),Reflect.get(t,"size",t)}function Bl(t){t=Se(t);const e=Se(this);return da(e).has.call(e,t)||(e.add(t),ii(e,"add",t,t)),this}function Nl(t,e){e=Se(e);const r=Se(this),{has:i,get:n}=da(r);let s=i.call(r,t);s?Gf(r,i,t):(t=Se(t),s=i.call(r,t));const a=n.call(r,t);return r.set(t,e),s?zf(e,a)&&ii(r,"set",t,e,a):ii(r,"add",t,e),this}function Vl(t){const e=Se(this),{has:r,get:i}=da(e);let n=r.call(e,t);n?Gf(e,r,t):(t=Se(t),n=r.call(e,t));const s=i?i.call(e,t):void 0,a=e.delete(t);return n&&ii(e,"delete",t,void 0,s),a}function Gl(){const t=Se(this),e=t.size!==0,r=In(t)?new Map(t):new Set(t),i=t.clear();return e&&ii(t,"clear",void 0,void 0,r),i}function ds(t,e){return function(i,n){const s=this,a=s.__v_raw,o=Se(a),l=e?rl:t?tl:el;return!t&&hr(o,"iterate",Ti),a.forEach((u,f)=>i.call(n,l(u),l(f),s))}}function ps(t,e,r){return function(...i){const n=this.__v_raw,s=Se(n),a=In(s),o=t==="entries"||t===Symbol.iterator&&a,l=t==="keys"&&a,u=n[t](...i),f=r?rl:e?tl:el;return!e&&hr(s,"iterate",l?so:Ti),{next(){const{value:c,done:p}=u.next();return p?{value:c,done:p}:{value:o?[f(c[0]),f(c[1])]:f(c),done:p}},[Symbol.iterator](){return this}}}}function Hr(t){return function(...e){{const r=e[0]?`on key "${e[0]}" `:"";console.warn(`${Kp(t)} operation ${r}failed: target is readonly.`,Se(this))}return t==="delete"?!1:this}}function gh(){const t={get(s){return us(this,s)},get size(){return cs(this)},has:fs,add:Bl,set:Nl,delete:Vl,clear:Gl,forEach:ds(!1,!1)},e={get(s){return us(this,s,!1,!0)},get size(){return cs(this)},has:fs,add:Bl,set:Nl,delete:Vl,clear:Gl,forEach:ds(!1,!0)},r={get(s){return us(this,s,!0)},get size(){return cs(this,!0)},has(s){return fs.call(this,s,!0)},add:Hr("add"),set:Hr("set"),delete:Hr("delete"),clear:Hr("clear"),forEach:ds(!0,!1)},i={get(s){return us(this,s,!0,!0)},get size(){return cs(this,!0)},has(s){return fs.call(this,s,!0)},add:Hr("add"),set:Hr("set"),delete:Hr("delete"),clear:Hr("clear"),forEach:ds(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach(s=>{t[s]=ps(s,!1,!1),r[s]=ps(s,!0,!1),e[s]=ps(s,!1,!0),i[s]=ps(s,!0,!0)}),[t,r,e,i]}var[_h,mh,vh,yh]=gh();function Vf(t,e){const r=e?t?yh:vh:t?mh:_h;return(i,n,s)=>n==="__v_isReactive"?!t:n==="__v_isReadonly"?t:n==="__v_raw"?i:Reflect.get(fa(r,n)&&n in i?r:i,n,s)}var xh={get:Vf(!1,!1)},bh={get:Vf(!0,!1)};function Gf(t,e,r){const i=Se(r);if(i!==r&&e.call(t,i)){const n=Rf(t);console.warn(`Reactive ${n} contains both the raw and reactive versions of the same object${n==="Map"?" as keys":""}, which can lead to inconsistencies. Avoid differentiating between the raw and reactive versions of an object and only use the reactive version if possible.`)}}var Hf=new WeakMap,Sh=new WeakMap,Yf=new WeakMap,wh=new WeakMap;function Th(t){switch(t){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Ch(t){return t.__v_skip||!Object.isExtensible(t)?0:Th(Rf(t))}function il(t){return t&&t.__v_isReadonly?t:Wf(t,!1,ph,xh,Hf)}function jf(t){return Wf(t,!0,hh,bh,Yf)}function Wf(t,e,r,i,n){if(!ca(t))return console.warn(`value cannot be made reactive: ${String(t)}`),t;if(t.__v_raw&&!(e&&t.__v_isReactive))return t;const s=n.get(t);if(s)return s;const a=Ch(t);if(a===0)return t;const o=new Proxy(t,a===2?i:r);return n.set(t,o),o}function Se(t){return t&&Se(t.__v_raw)||t}function ao(t){return!!(t&&t.__v_isRef===!0)}gr("nextTick",()=>Xo);gr("dispatch",t=>Dn.bind(Dn,t));gr("watch",(t,{evaluateLater:e,cleanup:r})=>(i,n)=>{let s=e(i),o=Gu(()=>{let l;return s(u=>l=u),l},n);r(o)});gr("store",$p);gr("data",t=>Ku(t));gr("root",t=>oa(t));gr("refs",t=>(t._x_refs_proxy||(t._x_refs_proxy=ns(Eh(t))),t._x_refs_proxy));function Eh(t){let e=[];return pn(t,r=>{r._x_refs&&e.push(r._x_refs)}),e}var ba={};function qf(t){return ba[t]||(ba[t]=0),++ba[t]}function Ph(t,e){return pn(t,r=>{if(r._x_ids&&r._x_ids[e])return!0})}function Mh(t,e){t._x_ids||(t._x_ids={}),t._x_ids[e]||(t._x_ids[e]=qf(e))}gr("id",(t,{cleanup:e})=>(r,i=null)=>{let n=`${r}${i?`-${i}`:""}`;return Oh(t,n,e,()=>{let s=Ph(t,r),a=s?s._x_ids[r]:qf(r);return i?`${r}-${a}-${i}`:`${r}-${a}`})});ua((t,e)=>{t._x_id&&(e._x_id=t._x_id)});function Oh(t,e,r,i){if(t._x_id||(t._x_id={}),t._x_id[e])return t._x_id[e];let n=i();return t._x_id[e]=n,r(()=>{delete t._x_id[e]}),n}gr("el",t=>t);Xf("Focus","focus","focus");Xf("Persist","persist","persist");function Xf(t,e,r){gr(e,i=>ir(`You can't use [$${e}] without first installing the "${t}" plugin here: https://alpinejs.dev/plugins/${r}`,i))}Ye("modelable",(t,{expression:e},{effect:r,evaluateLater:i,cleanup:n})=>{let s=i(e),a=()=>{let f;return s(c=>f=c),f},o=i(`${e} = __placeholder`),l=f=>o(()=>{},{scope:{__placeholder:f}}),u=a();l(u),queueMicrotask(()=>{if(!t._x_model)return;t._x_removeModelListeners.default();let f=t._x_model.get,c=t._x_model.set,p=Af({get(){return f()},set(d){c(d)}},{get(){return a()},set(d){l(d)}});n(p)})});Ye("teleport",(t,{modifiers:e,expression:r},{cleanup:i})=>{t.tagName.toLowerCase()!=="template"&&ir("x-teleport can only be used on a <template> tag",t);let n=Hl(r),s=t.content.cloneNode(!0).firstElementChild;t._x_teleport=s,s._x_teleportBack=t,t.setAttribute("data-teleport-template",!0),s.setAttribute("data-teleport-target",!0),t._x_forwardEvents&&t._x_forwardEvents.forEach(o=>{s.addEventListener(o,l=>{l.stopPropagation(),t.dispatchEvent(new l.constructor(l.type,l))})}),is(s,{},t);let a=(o,l,u)=>{u.includes("prepend")?l.parentNode.insertBefore(o,l):u.includes("append")?l.parentNode.insertBefore(o,l.nextSibling):l.appendChild(o)};ke(()=>{a(s,n,e),li(()=>{$r(s)})()}),t._x_teleportPutBack=()=>{let o=Hl(r);ke(()=>{a(t._x_teleport,o,e)})},i(()=>ke(()=>{s.remove(),hn(s)}))});var Ah=document.createElement("div");function Hl(t){let e=li(()=>document.querySelector(t),()=>Ah)();return e||ir(`Cannot find x-teleport element for selector: "${t}"`),e}var Uf=()=>{};Uf.inline=(t,{modifiers:e},{cleanup:r})=>{e.includes("self")?t._x_ignoreSelf=!0:t._x_ignore=!0,r(()=>{e.includes("self")?delete t._x_ignoreSelf:delete t._x_ignore})};Ye("ignore",Uf);Ye("effect",li((t,{expression:e},{effect:r})=>{r(Ot(t,e))}));function oo(t,e,r,i){let n=t,s=l=>i(l),a={},o=(l,u)=>f=>u(l,f);if(r.includes("dot")&&(e=kh(e)),r.includes("camel")&&(e=Lh(e)),r.includes("passive")&&(a.passive=!0),r.includes("capture")&&(a.capture=!0),r.includes("window")&&(n=window),r.includes("document")&&(n=document),r.includes("debounce")){let l=r[r.indexOf("debounce")+1]||"invalid-wait",u=Ws(l.split("ms")[0])?Number(l.split("ms")[0]):250;s=Mf(s,u)}if(r.includes("throttle")){let l=r[r.indexOf("throttle")+1]||"invalid-wait",u=Ws(l.split("ms")[0])?Number(l.split("ms")[0]):250;s=Of(s,u)}return r.includes("prevent")&&(s=o(s,(l,u)=>{u.preventDefault(),l(u)})),r.includes("stop")&&(s=o(s,(l,u)=>{u.stopPropagation(),l(u)})),r.includes("once")&&(s=o(s,(l,u)=>{l(u),n.removeEventListener(e,s,a)})),(r.includes("away")||r.includes("outside"))&&(n=document,s=o(s,(l,u)=>{t.contains(u.target)||u.target.isConnected!==!1&&(t.offsetWidth<1&&t.offsetHeight<1||t._x_isShown!==!1&&l(u))})),r.includes("self")&&(s=o(s,(l,u)=>{u.target===t&&l(u)})),(Ih(e)||Kf(e))&&(s=o(s,(l,u)=>{Rh(u,r)||l(u)})),n.addEventListener(e,s,a),()=>{n.removeEventListener(e,s,a)}}function kh(t){return t.replace(/-/g,".")}function Lh(t){return t.toLowerCase().replace(/-(\w)/g,(e,r)=>r.toUpperCase())}function Ws(t){return!Array.isArray(t)&&!isNaN(t)}function Dh(t){return[" ","_"].includes(t)?t:t.replace(/([a-z])([A-Z])/g,"$1-$2").replace(/[_\s]/,"-").toLowerCase()}function Ih(t){return["keydown","keyup"].includes(t)}function Kf(t){return["contextmenu","click","mouse"].some(e=>t.includes(e))}function Rh(t,e){let r=e.filter(s=>!["window","document","prevent","stop","once","capture","self","away","outside","passive"].includes(s));if(r.includes("debounce")){let s=r.indexOf("debounce");r.splice(s,Ws((r[s+1]||"invalid-wait").split("ms")[0])?2:1)}if(r.includes("throttle")){let s=r.indexOf("throttle");r.splice(s,Ws((r[s+1]||"invalid-wait").split("ms")[0])?2:1)}if(r.length===0||r.length===1&&Yl(t.key).includes(r[0]))return!1;const n=["ctrl","shift","alt","meta","cmd","super"].filter(s=>r.includes(s));return r=r.filter(s=>!n.includes(s)),!(n.length>0&&n.filter(a=>((a==="cmd"||a==="super")&&(a="meta"),t[`${a}Key`])).length===n.length&&(Kf(t.type)||Yl(t.key).includes(r[0])))}function Yl(t){if(!t)return[];t=Dh(t);let e={ctrl:"control",slash:"/",space:" ",spacebar:" ",cmd:"meta",esc:"escape",up:"arrow-up",down:"arrow-down",left:"arrow-left",right:"arrow-right",period:".",comma:",",equal:"=",minus:"-",underscore:"_"};return e[t]=t,Object.keys(e).map(r=>{if(e[r]===t)return r}).filter(r=>r)}Ye("model",(t,{modifiers:e,expression:r},{effect:i,cleanup:n})=>{let s=t;e.includes("parent")&&(s=t.parentNode);let a=Ot(s,r),o;typeof r=="string"?o=Ot(s,`${r} = __placeholder`):typeof r=="function"&&typeof r()=="string"?o=Ot(s,`${r()} = __placeholder`):o=()=>{};let l=()=>{let p;return a(d=>p=d),jl(p)?p.get():p},u=p=>{let d;a(_=>d=_),jl(d)?d.set(p):o(()=>{},{scope:{__placeholder:p}})};typeof r=="string"&&t.type==="radio"&&ke(()=>{t.hasAttribute("name")||t.setAttribute("name",r)});var f=t.tagName.toLowerCase()==="select"||["checkbox","radio"].includes(t.type)||e.includes("lazy")?"change":"input";let c=ri?()=>{}:oo(t,f,e,p=>{u(Sa(t,e,p,l()))});if(e.includes("fill")&&([void 0,null,""].includes(l())||Ko(t)&&Array.isArray(l())||t.tagName.toLowerCase()==="select"&&t.multiple)&&u(Sa(t,e,{target:t},l())),t._x_removeModelListeners||(t._x_removeModelListeners={}),t._x_removeModelListeners.default=c,n(()=>t._x_removeModelListeners.default()),t.form){let p=oo(t.form,"reset",[],d=>{Xo(()=>t._x_model&&t._x_model.set(Sa(t,e,{target:t},l())))});n(()=>p())}t._x_model={get(){return l()},set(p){u(p)}},t._x_forceModelUpdate=p=>{p===void 0&&typeof r=="string"&&r.match(/\./)&&(p=""),window.fromModel=!0,ke(()=>wf(t,"value",p)),delete window.fromModel},i(()=>{let p=l();e.includes("unintrusive")&&document.activeElement.isSameNode(t)||t._x_forceModelUpdate(p)})});function Sa(t,e,r,i){return ke(()=>{if(r instanceof CustomEvent&&r.detail!==void 0)return r.detail!==null&&r.detail!==void 0?r.detail:r.target.value;if(Ko(t))if(Array.isArray(i)){let n=null;return e.includes("number")?n=wa(r.target.value):e.includes("boolean")?n=Ls(r.target.value):n=r.target.value,r.target.checked?i.includes(n)?i:i.concat([n]):i.filter(s=>!zh(s,n))}else return r.target.checked;else{if(t.tagName.toLowerCase()==="select"&&t.multiple)return e.includes("number")?Array.from(r.target.selectedOptions).map(n=>{let s=n.value||n.text;return wa(s)}):e.includes("boolean")?Array.from(r.target.selectedOptions).map(n=>{let s=n.value||n.text;return Ls(s)}):Array.from(r.target.selectedOptions).map(n=>n.value||n.text);{let n;return Pf(t)?r.target.checked?n=r.target.value:n=i:n=r.target.value,e.includes("number")?wa(n):e.includes("boolean")?Ls(n):e.includes("trim")?n.trim():n}}})}function wa(t){let e=t?parseFloat(t):null;return Fh(e)?e:t}function zh(t,e){return t==e}function Fh(t){return!Array.isArray(t)&&!isNaN(t)}function jl(t){return t!==null&&typeof t=="object"&&typeof t.get=="function"&&typeof t.set=="function"}Ye("cloak",t=>queueMicrotask(()=>ke(()=>t.removeAttribute(dn("cloak")))));mf(()=>`[${dn("init")}]`);Ye("init",li((t,{expression:e},{evaluate:r})=>typeof e=="string"?!!e.trim()&&r(e,{},!1):r(e,{},!1)));Ye("text",(t,{expression:e},{effect:r,evaluateLater:i})=>{let n=i(e);r(()=>{n(s=>{ke(()=>{t.textContent=s})})})});Ye("html",(t,{expression:e},{effect:r,evaluateLater:i})=>{let n=i(e);r(()=>{n(s=>{ke(()=>{t.innerHTML=s,t._x_ignoreSelf=!0,$r(t),delete t._x_ignoreSelf})})})});jo(of(":",lf(dn("bind:"))));var Jf=(t,{value:e,modifiers:r,expression:i,original:n},{effect:s,cleanup:a})=>{if(!e){let l={};Np(l),Ot(t,i)(f=>{Lf(t,f,n)},{scope:l});return}if(e==="key")return $h(t,i);if(t._x_inlineBindings&&t._x_inlineBindings[e]&&t._x_inlineBindings[e].extract)return;let o=Ot(t,i);s(()=>o(l=>{l===void 0&&typeof i=="string"&&i.match(/\./)&&(l=""),ke(()=>wf(t,e,l,r))})),a(()=>{t._x_undoAddedClasses&&t._x_undoAddedClasses(),t._x_undoAddedStyles&&t._x_undoAddedStyles()})};Jf.inline=(t,{value:e,modifiers:r,expression:i})=>{e&&(t._x_inlineBindings||(t._x_inlineBindings={}),t._x_inlineBindings[e]={expression:i,extract:!1})};Ye("bind",Jf);function $h(t,e){t._x_keyExpression=e}_f(()=>`[${dn("data")}]`);Ye("data",(t,{expression:e},{cleanup:r})=>{if(Bh(t))return;e=e===""?"{}":e;let i={};Ka(i,t);let n={};Gp(n,i);let s=Si(t,e,{scope:n});(s===void 0||s===!0)&&(s={}),Ka(s,t);let a=fn(s);Ju(a);let o=is(t,a);a.init&&Si(t,a.init),r(()=>{a.destroy&&Si(t,a.destroy),o()})});ua((t,e)=>{t._x_dataStack&&(e._x_dataStack=t._x_dataStack,e.setAttribute("data-has-alpine-state",!0))});function Bh(t){return ri?io?!0:t.hasAttribute("data-has-alpine-state"):!1}Ye("show",(t,{modifiers:e,expression:r},{effect:i})=>{let n=Ot(t,r);t._x_doHide||(t._x_doHide=()=>{ke(()=>{t.style.setProperty("display","none",e.includes("important")?"important":void 0)})}),t._x_doShow||(t._x_doShow=()=>{ke(()=>{t.style.length===1&&t.style.display==="none"?t.removeAttribute("style"):t.style.removeProperty("display")})});let s=()=>{t._x_doHide(),t._x_isShown=!1},a=()=>{t._x_doShow(),t._x_isShown=!0},o=()=>setTimeout(a),l=to(c=>c?a():s(),c=>{typeof t._x_toggleAndCascadeWithTransitions=="function"?t._x_toggleAndCascadeWithTransitions(t,c,a,s):c?o():s()}),u,f=!0;i(()=>n(c=>{!f&&c===u||(e.includes("immediate")&&(c?o():s()),l(c),u=c,f=!1)}))});Ye("for",(t,{expression:e},{effect:r,cleanup:i})=>{let n=Vh(e),s=Ot(t,n.items),a=Ot(t,t._x_keyExpression||"index");t._x_prevKeys=[],t._x_lookup={},r(()=>Nh(t,n,s,a)),i(()=>{Object.values(t._x_lookup).forEach(o=>ke(()=>{hn(o),o.remove()})),delete t._x_prevKeys,delete t._x_lookup})});function Nh(t,e,r,i){let n=a=>typeof a=="object"&&!Array.isArray(a),s=t;r(a=>{Gh(a)&&a>=0&&(a=Array.from(Array(a).keys(),m=>m+1)),a===void 0&&(a=[]);let o=t._x_lookup,l=t._x_prevKeys,u=[],f=[];if(n(a))a=Object.entries(a).map(([m,g])=>{let v=Wl(e,g,m,a);i(x=>{f.includes(x)&&ir("Duplicate key on x-for",t),f.push(x)},{scope:{index:m,...v}}),u.push(v)});else for(let m=0;m<a.length;m++){let g=Wl(e,a[m],m,a);i(v=>{f.includes(v)&&ir("Duplicate key on x-for",t),f.push(v)},{scope:{index:m,...g}}),u.push(g)}let c=[],p=[],d=[],_=[];for(let m=0;m<l.length;m++){let g=l[m];f.indexOf(g)===-1&&d.push(g)}l=l.filter(m=>!d.includes(m));let h="template";for(let m=0;m<f.length;m++){let g=f[m],v=l.indexOf(g);if(v===-1)l.splice(m,0,g),c.push([h,m]);else if(v!==m){let x=l.splice(m,1)[0],y=l.splice(v-1,1)[0];l.splice(m,0,y),l.splice(v,0,x),p.push([x,y])}else _.push(g);h=g}for(let m=0;m<d.length;m++){let g=d[m];g in o&&(ke(()=>{hn(o[g]),o[g].remove()}),delete o[g])}for(let m=0;m<p.length;m++){let[g,v]=p[m],x=o[g],y=o[v],b=document.createElement("div");ke(()=>{y||ir('x-for ":key" is undefined or invalid',s,v,o),y.after(b),x.after(y),y._x_currentIfEl&&y.after(y._x_currentIfEl),b.before(x),x._x_currentIfEl&&x.after(x._x_currentIfEl),b.remove()}),y._x_refreshXForScope(u[f.indexOf(v)])}for(let m=0;m<c.length;m++){let[g,v]=c[m],x=g==="template"?s:o[g];x._x_currentIfEl&&(x=x._x_currentIfEl);let y=u[v],b=f[v],w=document.importNode(s.content,!0).firstElementChild,C=fn(y);is(w,C,s),w._x_refreshXForScope=O=>{Object.entries(O).forEach(([M,A])=>{C[M]=A})},ke(()=>{x.after(w),li(()=>$r(w))()}),typeof b=="object"&&ir("x-for key cannot be an object, it must be a string or an integer",s),o[b]=w}for(let m=0;m<_.length;m++)o[_[m]]._x_refreshXForScope(u[f.indexOf(_[m])]);s._x_prevKeys=f})}function Vh(t){let e=/,([^,\}\]]*)(?:,([^,\}\]]*))?$/,r=/^\s*\(|\)\s*$/g,i=/([\s\S]*?)\s+(?:in|of)\s+([\s\S]*)/,n=t.match(i);if(!n)return;let s={};s.items=n[2].trim();let a=n[1].replace(r,"").trim(),o=a.match(e);return o?(s.item=a.replace(e,"").trim(),s.index=o[1].trim(),o[2]&&(s.collection=o[2].trim())):s.item=a,s}function Wl(t,e,r,i){let n={};return/^\[.*\]$/.test(t.item)&&Array.isArray(e)?t.item.replace("[","").replace("]","").split(",").map(a=>a.trim()).forEach((a,o)=>{n[a]=e[o]}):/^\{.*\}$/.test(t.item)&&!Array.isArray(e)&&typeof e=="object"?t.item.replace("{","").replace("}","").split(",").map(a=>a.trim()).forEach(a=>{n[a]=e[a]}):n[t.item]=e,t.index&&(n[t.index]=r),t.collection&&(n[t.collection]=i),n}function Gh(t){return!Array.isArray(t)&&!isNaN(t)}function Zf(){}Zf.inline=(t,{expression:e},{cleanup:r})=>{let i=oa(t);i._x_refs||(i._x_refs={}),i._x_refs[e]=t,r(()=>delete i._x_refs[e])};Ye("ref",Zf);Ye("if",(t,{expression:e},{effect:r,cleanup:i})=>{t.tagName.toLowerCase()!=="template"&&ir("x-if can only be used on a <template> tag",t);let n=Ot(t,e),s=()=>{if(t._x_currentIfEl)return t._x_currentIfEl;let o=t.content.cloneNode(!0).firstElementChild;return is(o,{},t),ke(()=>{t.after(o),li(()=>$r(o))()}),t._x_currentIfEl=o,t._x_undoIf=()=>{ke(()=>{hn(o),o.remove()}),delete t._x_currentIfEl},o},a=()=>{t._x_undoIf&&(t._x_undoIf(),delete t._x_undoIf)};r(()=>n(o=>{o?s():a()})),i(()=>t._x_undoIf&&t._x_undoIf())});Ye("id",(t,{expression:e},{evaluate:r})=>{r(e).forEach(n=>Mh(t,n))});ua((t,e)=>{t._x_ids&&(e._x_ids=t._x_ids)});jo(of("@",lf(dn("on:"))));Ye("on",li((t,{value:e,modifiers:r,expression:i},{cleanup:n})=>{let s=i?Ot(t,i):()=>{};t.tagName.toLowerCase()==="template"&&(t._x_forwardEvents||(t._x_forwardEvents=[]),t._x_forwardEvents.includes(e)||t._x_forwardEvents.push(e));let a=oo(t,e,r,o=>{s(()=>{},{scope:{$event:o},params:[o]})});n(()=>a())}));pa("Collapse","collapse","collapse");pa("Intersect","intersect","intersect");pa("Focus","trap","focus");pa("Mask","mask","mask");function pa(t,e,r){Ye(e,i=>ir(`You can't use [x-${e}] without first installing the "${t}" plugin here: https://alpinejs.dev/plugins/${r}`,i))}ss.setEvaluator(rf);ss.setReactivityEngine({reactive:il,effect:Zp,release:Qp,raw:Se});var Hh=ss,nl=Hh;function Yh(t){t.directive("collapse",e),e.inline=(r,{modifiers:i})=>{i.includes("min")&&(r._x_doShow=()=>{},r._x_doHide=()=>{})};function e(r,{modifiers:i}){let n=ql(i,"duration",250)/1e3,s=ql(i,"min",0),a=!i.includes("min");r._x_isShown||(r.style.height=`${s}px`),!r._x_isShown&&a&&(r.hidden=!0),r._x_isShown||(r.style.overflow="hidden");let o=(u,f)=>{let c=t.setStyles(u,f);return f.height?()=>{}:c},l={transitionProperty:"height",transitionDuration:`${n}s`,transitionTimingFunction:"cubic-bezier(0.4, 0.0, 0.2, 1)"};r._x_transition={in(u=()=>{},f=()=>{}){a&&(r.hidden=!1),a&&(r.style.display=null);let c=r.getBoundingClientRect().height;r.style.height="auto";let p=r.getBoundingClientRect().height;c===p&&(c=s),t.transition(r,t.setStyles,{during:l,start:{height:c+"px"},end:{height:p+"px"}},()=>r._x_isShown=!0,()=>{Math.abs(r.getBoundingClientRect().height-p)<1&&(r.style.overflow=null)})},out(u=()=>{},f=()=>{}){let c=r.getBoundingClientRect().height;t.transition(r,o,{during:l,start:{height:c+"px"},end:{height:s+"px"}},()=>r.style.overflow="hidden",()=>{r._x_isShown=!1,r.style.height==`${s}px`&&a&&(r.style.display="none",r.hidden=!0)})}}}}function ql(t,e,r){if(t.indexOf(e)===-1)return r;const i=t[t.indexOf(e)+1];if(!i)return r;if(e==="duration"){let n=i.match(/([0-9]+)ms/);if(n)return n[1]}if(e==="min"){let n=i.match(/([0-9]+)px/);if(n)return n[1]}return i}var jh=Yh;const Wh={init(){const t=document.querySelector(".js-cursor-dot");let e=0,r=0;function i(){t.style.left=e+"px",t.style.top=r+"px",requestAnimationFrame(i)}document.addEventListener("mousemove",n=>{var s;e=n.clientX,r=n.clientY;var a=n.target;a.closest(".js-no-custom-cursor")?t.classList.add("lg:hidden"):t.classList.remove("lg:hidden");var o=/bg-(.*?)-accent/;if(s=a.closest(".js-view-case-cursor")){var l=s.dataset.bg??"bg-accent";t.classList.forEach(function(u){o.test(u)&&t.classList.remove(u)}),t.classList.add("!w-12","!h-12","!-translate-x-7","!-translate-y-7",l),t.classList.remove("bg-accent"),t.querySelector("span").classList.remove("hidden")}else t.classList.remove("!w-12","!h-12","!-translate-x-7","!-translate-y-7"),t.classList.forEach(function(u){o.test(u)&&t.classList.remove(u)}),t.classList.add("bg-accent"),t.querySelector("span").classList.add("hidden")}),requestAnimationFrame(i)}};function Xl(t){return t!==null&&typeof t=="object"&&"constructor"in t&&t.constructor===Object}function sl(t={},e={}){Object.keys(e).forEach(r=>{typeof t[r]>"u"?t[r]=e[r]:Xl(e[r])&&Xl(t[r])&&Object.keys(e[r]).length>0&&sl(t[r],e[r])})}const Qf={body:{},addEventListener(){},removeEventListener(){},activeElement:{blur(){},nodeName:""},querySelector(){return null},querySelectorAll(){return[]},getElementById(){return null},createEvent(){return{initEvent(){}}},createElement(){return{children:[],childNodes:[],style:{},setAttribute(){},getElementsByTagName(){return[]}}},createElementNS(){return{}},importNode(){return null},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function Mr(){const t=typeof document<"u"?document:{};return sl(t,Qf),t}const qh={document:Qf,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState(){},pushState(){},go(){},back(){}},CustomEvent:function(){return this},addEventListener(){},removeEventListener(){},getComputedStyle(){return{getPropertyValue(){return""}}},Image(){},Date(){},screen:{},setTimeout(){},clearTimeout(){},matchMedia(){return{}},requestAnimationFrame(t){return typeof setTimeout>"u"?(t(),null):setTimeout(t,0)},cancelAnimationFrame(t){typeof setTimeout>"u"||clearTimeout(t)}};function Zt(){const t=typeof window<"u"?window:{};return sl(t,qh),t}function Xh(t){const e=t;Object.keys(e).forEach(r=>{try{e[r]=null}catch{}try{delete e[r]}catch{}})}function lo(t,e=0){return setTimeout(t,e)}function qs(){return Date.now()}function Uh(t){const e=Zt();let r;return e.getComputedStyle&&(r=e.getComputedStyle(t,null)),!r&&t.currentStyle&&(r=t.currentStyle),r||(r=t.style),r}function Kh(t,e="x"){const r=Zt();let i,n,s;const a=Uh(t);return r.WebKitCSSMatrix?(n=a.transform||a.webkitTransform,n.split(",").length>6&&(n=n.split(", ").map(o=>o.replace(",",".")).join(", ")),s=new r.WebKitCSSMatrix(n==="none"?"":n)):(s=a.MozTransform||a.OTransform||a.MsTransform||a.msTransform||a.transform||a.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,"),i=s.toString().split(",")),e==="x"&&(r.WebKitCSSMatrix?n=s.m41:i.length===16?n=parseFloat(i[12]):n=parseFloat(i[4])),e==="y"&&(r.WebKitCSSMatrix?n=s.m42:i.length===16?n=parseFloat(i[13]):n=parseFloat(i[5])),n||0}function hs(t){return typeof t=="object"&&t!==null&&t.constructor&&Object.prototype.toString.call(t).slice(8,-1)==="Object"}function Jh(t){return typeof window<"u"&&typeof window.HTMLElement<"u"?t instanceof HTMLElement:t&&(t.nodeType===1||t.nodeType===11)}function Ht(...t){const e=Object(t[0]),r=["__proto__","constructor","prototype"];for(let i=1;i<t.length;i+=1){const n=t[i];if(n!=null&&!Jh(n)){const s=Object.keys(Object(n)).filter(a=>r.indexOf(a)<0);for(let a=0,o=s.length;a<o;a+=1){const l=s[a],u=Object.getOwnPropertyDescriptor(n,l);u!==void 0&&u.enumerable&&(hs(e[l])&&hs(n[l])?n[l].__swiper__?e[l]=n[l]:Ht(e[l],n[l]):!hs(e[l])&&hs(n[l])?(e[l]={},n[l].__swiper__?e[l]=n[l]:Ht(e[l],n[l])):e[l]=n[l])}}}return e}function gs(t,e,r){t.style.setProperty(e,r)}function ec({swiper:t,targetPosition:e,side:r}){const i=Zt(),n=-t.translate;let s=null,a;const o=t.params.speed;t.wrapperEl.style.scrollSnapType="none",i.cancelAnimationFrame(t.cssModeFrameID);const l=e>n?"next":"prev",u=(c,p)=>l==="next"&&c>=p||l==="prev"&&c<=p,f=()=>{a=new Date().getTime(),s===null&&(s=a);const c=Math.max(Math.min((a-s)/o,1),0),p=.5-Math.cos(c*Math.PI)/2;let d=n+p*(e-n);if(u(d,e)&&(d=e),t.wrapperEl.scrollTo({[r]:d}),u(d,e)){t.wrapperEl.style.overflow="hidden",t.wrapperEl.style.scrollSnapType="",setTimeout(()=>{t.wrapperEl.style.overflow="",t.wrapperEl.scrollTo({[r]:d})}),i.cancelAnimationFrame(t.cssModeFrameID);return}t.cssModeFrameID=i.requestAnimationFrame(f)};f()}function Cr(t,e=""){return[...t.children].filter(r=>r.matches(e))}function tc(t,e=[]){const r=document.createElement(t);return r.classList.add(...Array.isArray(e)?e:[e]),r}function Zh(t,e){const r=[];for(;t.previousElementSibling;){const i=t.previousElementSibling;e?i.matches(e)&&r.push(i):r.push(i),t=i}return r}function Qh(t,e){const r=[];for(;t.nextElementSibling;){const i=t.nextElementSibling;e?i.matches(e)&&r.push(i):r.push(i),t=i}return r}function qr(t,e){return Zt().getComputedStyle(t,null).getPropertyValue(e)}function Xs(t){let e=t,r;if(e){for(r=0;(e=e.previousSibling)!==null;)e.nodeType===1&&(r+=1);return r}}function rc(t,e){const r=[];let i=t.parentElement;for(;i;)e?i.matches(e)&&r.push(i):r.push(i),i=i.parentElement;return r}function uo(t,e,r){const i=Zt();return r?t[e==="width"?"offsetWidth":"offsetHeight"]+parseFloat(i.getComputedStyle(t,null).getPropertyValue(e==="width"?"margin-right":"margin-top"))+parseFloat(i.getComputedStyle(t,null).getPropertyValue(e==="width"?"margin-left":"margin-bottom")):t.offsetWidth}let Ta;function eg(){const t=Zt(),e=Mr();return{smoothScroll:e.documentElement&&e.documentElement.style&&"scrollBehavior"in e.documentElement.style,touch:!!("ontouchstart"in t||t.DocumentTouch&&e instanceof t.DocumentTouch)}}function ic(){return Ta||(Ta=eg()),Ta}let Ca;function tg({userAgent:t}={}){const e=ic(),r=Zt(),i=r.navigator.platform,n=t||r.navigator.userAgent,s={ios:!1,android:!1},a=r.screen.width,o=r.screen.height,l=n.match(/(Android);?[\s\/]+([\d.]+)?/);let u=n.match(/(iPad).*OS\s([\d_]+)/);const f=n.match(/(iPod)(.*OS\s([\d_]+))?/),c=!u&&n.match(/(iPhone\sOS|iOS)\s([\d_]+)/),p=i==="Win32";let d=i==="MacIntel";const _=["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"];return!u&&d&&e.touch&&_.indexOf(`${a}x${o}`)>=0&&(u=n.match(/(Version)\/([\d.]+)/),u||(u=[0,1,"13_0_0"]),d=!1),l&&!p&&(s.os="android",s.android=!0),(u||c||f)&&(s.os="ios",s.ios=!0),s}function rg(t={}){return Ca||(Ca=tg(t)),Ca}let Ea;function ig(){const t=Zt();let e=!1;function r(){const i=t.navigator.userAgent.toLowerCase();return i.indexOf("safari")>=0&&i.indexOf("chrome")<0&&i.indexOf("android")<0}if(r()){const i=String(t.navigator.userAgent);if(i.includes("Version/")){const[n,s]=i.split("Version/")[1].split(" ")[0].split(".").map(a=>Number(a));e=n<16||n===16&&s<2}}return{isSafari:e||r(),needPerspectiveFix:e,isWebView:/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(t.navigator.userAgent)}}function ng(){return Ea||(Ea=ig()),Ea}function sg({swiper:t,on:e,emit:r}){const i=Zt();let n=null,s=null;const a=()=>{!t||t.destroyed||!t.initialized||(r("beforeResize"),r("resize"))},o=()=>{!t||t.destroyed||!t.initialized||(n=new ResizeObserver(f=>{s=i.requestAnimationFrame(()=>{const{width:c,height:p}=t;let d=c,_=p;f.forEach(({contentBoxSize:h,contentRect:m,target:g})=>{g&&g!==t.el||(d=m?m.width:(h[0]||h).inlineSize,_=m?m.height:(h[0]||h).blockSize)}),(d!==c||_!==p)&&a()})}),n.observe(t.el))},l=()=>{s&&i.cancelAnimationFrame(s),n&&n.unobserve&&t.el&&(n.unobserve(t.el),n=null)},u=()=>{!t||t.destroyed||!t.initialized||r("orientationchange")};e("init",()=>{if(t.params.resizeObserver&&typeof i.ResizeObserver<"u"){o();return}i.addEventListener("resize",a),i.addEventListener("orientationchange",u)}),e("destroy",()=>{l(),i.removeEventListener("resize",a),i.removeEventListener("orientationchange",u)})}function ag({swiper:t,extendParams:e,on:r,emit:i}){const n=[],s=Zt(),a=(u,f={})=>{const c=s.MutationObserver||s.WebkitMutationObserver,p=new c(d=>{if(t.__preventObserver__)return;if(d.length===1){i("observerUpdate",d[0]);return}const _=function(){i("observerUpdate",d[0])};s.requestAnimationFrame?s.requestAnimationFrame(_):s.setTimeout(_,0)});p.observe(u,{attributes:typeof f.attributes>"u"?!0:f.attributes,childList:typeof f.childList>"u"?!0:f.childList,characterData:typeof f.characterData>"u"?!0:f.characterData}),n.push(p)},o=()=>{if(t.params.observer){if(t.params.observeParents){const u=rc(t.el);for(let f=0;f<u.length;f+=1)a(u[f])}a(t.el,{childList:t.params.observeSlideChildren}),a(t.wrapperEl,{attributes:!1})}},l=()=>{n.forEach(u=>{u.disconnect()}),n.splice(0,n.length)};e({observer:!1,observeParents:!1,observeSlideChildren:!1}),r("init",o),r("destroy",l)}const og={on(t,e,r){const i=this;if(!i.eventsListeners||i.destroyed||typeof e!="function")return i;const n=r?"unshift":"push";return t.split(" ").forEach(s=>{i.eventsListeners[s]||(i.eventsListeners[s]=[]),i.eventsListeners[s][n](e)}),i},once(t,e,r){const i=this;if(!i.eventsListeners||i.destroyed||typeof e!="function")return i;function n(...s){i.off(t,n),n.__emitterProxy&&delete n.__emitterProxy,e.apply(i,s)}return n.__emitterProxy=e,i.on(t,n,r)},onAny(t,e){const r=this;if(!r.eventsListeners||r.destroyed||typeof t!="function")return r;const i=e?"unshift":"push";return r.eventsAnyListeners.indexOf(t)<0&&r.eventsAnyListeners[i](t),r},offAny(t){const e=this;if(!e.eventsListeners||e.destroyed||!e.eventsAnyListeners)return e;const r=e.eventsAnyListeners.indexOf(t);return r>=0&&e.eventsAnyListeners.splice(r,1),e},off(t,e){const r=this;return!r.eventsListeners||r.destroyed||!r.eventsListeners||t.split(" ").forEach(i=>{typeof e>"u"?r.eventsListeners[i]=[]:r.eventsListeners[i]&&r.eventsListeners[i].forEach((n,s)=>{(n===e||n.__emitterProxy&&n.__emitterProxy===e)&&r.eventsListeners[i].splice(s,1)})}),r},emit(...t){const e=this;if(!e.eventsListeners||e.destroyed||!e.eventsListeners)return e;let r,i,n;return typeof t[0]=="string"||Array.isArray(t[0])?(r=t[0],i=t.slice(1,t.length),n=e):(r=t[0].events,i=t[0].data,n=t[0].context||e),i.unshift(n),(Array.isArray(r)?r:r.split(" ")).forEach(a=>{e.eventsAnyListeners&&e.eventsAnyListeners.length&&e.eventsAnyListeners.forEach(o=>{o.apply(n,[a,...i])}),e.eventsListeners&&e.eventsListeners[a]&&e.eventsListeners[a].forEach(o=>{o.apply(n,i)})}),e}};function lg(){const t=this;let e,r;const i=t.el;typeof t.params.width<"u"&&t.params.width!==null?e=t.params.width:e=i.clientWidth,typeof t.params.height<"u"&&t.params.height!==null?r=t.params.height:r=i.clientHeight,!(e===0&&t.isHorizontal()||r===0&&t.isVertical())&&(e=e-parseInt(qr(i,"padding-left")||0,10)-parseInt(qr(i,"padding-right")||0,10),r=r-parseInt(qr(i,"padding-top")||0,10)-parseInt(qr(i,"padding-bottom")||0,10),Number.isNaN(e)&&(e=0),Number.isNaN(r)&&(r=0),Object.assign(t,{width:e,height:r,size:t.isHorizontal()?e:r}))}function ug(){const t=this;function e(k){return t.isHorizontal()?k:{width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"}[k]}function r(k,E){return parseFloat(k.getPropertyValue(e(E))||0)}const i=t.params,{wrapperEl:n,slidesEl:s,size:a,rtlTranslate:o,wrongRTL:l}=t,u=t.virtual&&i.virtual.enabled,f=u?t.virtual.slides.length:t.slides.length,c=Cr(s,`.${t.params.slideClass}, swiper-slide`),p=u?t.virtual.slides.length:c.length;let d=[];const _=[],h=[];let m=i.slidesOffsetBefore;typeof m=="function"&&(m=i.slidesOffsetBefore.call(t));let g=i.slidesOffsetAfter;typeof g=="function"&&(g=i.slidesOffsetAfter.call(t));const v=t.snapGrid.length,x=t.slidesGrid.length;let y=i.spaceBetween,b=-m,w=0,C=0;if(typeof a>"u")return;typeof y=="string"&&y.indexOf("%")>=0?y=parseFloat(y.replace("%",""))/100*a:typeof y=="string"&&(y=parseFloat(y)),t.virtualSize=-y,c.forEach(k=>{o?k.style.marginLeft="":k.style.marginRight="",k.style.marginBottom="",k.style.marginTop=""}),i.centeredSlides&&i.cssMode&&(gs(n,"--swiper-centered-offset-before",""),gs(n,"--swiper-centered-offset-after",""));const O=i.grid&&i.grid.rows>1&&t.grid;O&&t.grid.initSlides(p);let M;const A=i.slidesPerView==="auto"&&i.breakpoints&&Object.keys(i.breakpoints).filter(k=>typeof i.breakpoints[k].slidesPerView<"u").length>0;for(let k=0;k<p;k+=1){M=0;let E;if(c[k]&&(E=c[k]),O&&t.grid.updateSlide(k,E,p,e),!(c[k]&&qr(E,"display")==="none")){if(i.slidesPerView==="auto"){A&&(c[k].style[e("width")]="");const L=getComputedStyle(E),R=E.style.transform,H=E.style.webkitTransform;if(R&&(E.style.transform="none"),H&&(E.style.webkitTransform="none"),i.roundLengths)M=t.isHorizontal()?uo(E,"width",!0):uo(E,"height",!0);else{const F=r(L,"width"),B=r(L,"padding-left"),j=r(L,"padding-right"),Z=r(L,"margin-left"),S=r(L,"margin-right"),fe=L.getPropertyValue("box-sizing");if(fe&&fe==="border-box")M=F+Z+S;else{const{clientWidth:Le,offsetWidth:Ke}=E;M=F+B+j+Z+S+(Ke-Le)}}R&&(E.style.transform=R),H&&(E.style.webkitTransform=H),i.roundLengths&&(M=Math.floor(M))}else M=(a-(i.slidesPerView-1)*y)/i.slidesPerView,i.roundLengths&&(M=Math.floor(M)),c[k]&&(c[k].style[e("width")]=`${M}px`);c[k]&&(c[k].swiperSlideSize=M),h.push(M),i.centeredSlides?(b=b+M/2+w/2+y,w===0&&k!==0&&(b=b-a/2-y),k===0&&(b=b-a/2-y),Math.abs(b)<1/1e3&&(b=0),i.roundLengths&&(b=Math.floor(b)),C%i.slidesPerGroup===0&&d.push(b),_.push(b)):(i.roundLengths&&(b=Math.floor(b)),(C-Math.min(t.params.slidesPerGroupSkip,C))%t.params.slidesPerGroup===0&&d.push(b),_.push(b),b=b+M+y),t.virtualSize+=M+y,w=M,C+=1}}if(t.virtualSize=Math.max(t.virtualSize,a)+g,o&&l&&(i.effect==="slide"||i.effect==="coverflow")&&(n.style.width=`${t.virtualSize+y}px`),i.setWrapperSize&&(n.style[e("width")]=`${t.virtualSize+y}px`),O&&t.grid.updateWrapperSize(M,d,e),!i.centeredSlides){const k=[];for(let E=0;E<d.length;E+=1){let L=d[E];i.roundLengths&&(L=Math.floor(L)),d[E]<=t.virtualSize-a&&k.push(L)}d=k,Math.floor(t.virtualSize-a)-Math.floor(d[d.length-1])>1&&d.push(t.virtualSize-a)}if(u&&i.loop){const k=h[0]+y;if(i.slidesPerGroup>1){const E=Math.ceil((t.virtual.slidesBefore+t.virtual.slidesAfter)/i.slidesPerGroup),L=k*i.slidesPerGroup;for(let R=0;R<E;R+=1)d.push(d[d.length-1]+L)}for(let E=0;E<t.virtual.slidesBefore+t.virtual.slidesAfter;E+=1)i.slidesPerGroup===1&&d.push(d[d.length-1]+k),_.push(_[_.length-1]+k),t.virtualSize+=k}if(d.length===0&&(d=[0]),y!==0){const k=t.isHorizontal()&&o?"marginLeft":e("marginRight");c.filter((E,L)=>!i.cssMode||i.loop?!0:L!==c.length-1).forEach(E=>{E.style[k]=`${y}px`})}if(i.centeredSlides&&i.centeredSlidesBounds){let k=0;h.forEach(L=>{k+=L+(y||0)}),k-=y;const E=k-a;d=d.map(L=>L<=0?-m:L>E?E+g:L)}if(i.centerInsufficientSlides){let k=0;if(h.forEach(E=>{k+=E+(y||0)}),k-=y,k<a){const E=(a-k)/2;d.forEach((L,R)=>{d[R]=L-E}),_.forEach((L,R)=>{_[R]=L+E})}}if(Object.assign(t,{slides:c,snapGrid:d,slidesGrid:_,slidesSizesGrid:h}),i.centeredSlides&&i.cssMode&&!i.centeredSlidesBounds){gs(n,"--swiper-centered-offset-before",`${-d[0]}px`),gs(n,"--swiper-centered-offset-after",`${t.size/2-h[h.length-1]/2}px`);const k=-t.snapGrid[0],E=-t.slidesGrid[0];t.snapGrid=t.snapGrid.map(L=>L+k),t.slidesGrid=t.slidesGrid.map(L=>L+E)}if(p!==f&&t.emit("slidesLengthChange"),d.length!==v&&(t.params.watchOverflow&&t.checkOverflow(),t.emit("snapGridLengthChange")),_.length!==x&&t.emit("slidesGridLengthChange"),i.watchSlidesProgress&&t.updateSlidesOffset(),!u&&!i.cssMode&&(i.effect==="slide"||i.effect==="fade")){const k=`${i.containerModifierClass}backface-hidden`,E=t.el.classList.contains(k);p<=i.maxBackfaceHiddenSlides?E||t.el.classList.add(k):E&&t.el.classList.remove(k)}}function fg(t){const e=this,r=[],i=e.virtual&&e.params.virtual.enabled;let n=0,s;typeof t=="number"?e.setTransition(t):t===!0&&e.setTransition(e.params.speed);const a=o=>i?e.slides[e.getSlideIndexByData(o)]:e.slides[o];if(e.params.slidesPerView!=="auto"&&e.params.slidesPerView>1)if(e.params.centeredSlides)(e.visibleSlides||[]).forEach(o=>{r.push(o)});else for(s=0;s<Math.ceil(e.params.slidesPerView);s+=1){const o=e.activeIndex+s;if(o>e.slides.length&&!i)break;r.push(a(o))}else r.push(a(e.activeIndex));for(s=0;s<r.length;s+=1)if(typeof r[s]<"u"){const o=r[s].offsetHeight;n=o>n?o:n}(n||n===0)&&(e.wrapperEl.style.height=`${n}px`)}function cg(){const t=this,e=t.slides,r=t.isElement?t.isHorizontal()?t.wrapperEl.offsetLeft:t.wrapperEl.offsetTop:0;for(let i=0;i<e.length;i+=1)e[i].swiperSlideOffset=(t.isHorizontal()?e[i].offsetLeft:e[i].offsetTop)-r-t.cssOverflowAdjustment()}function dg(t=this&&this.translate||0){const e=this,r=e.params,{slides:i,rtlTranslate:n,snapGrid:s}=e;if(i.length===0)return;typeof i[0].swiperSlideOffset>"u"&&e.updateSlidesOffset();let a=-t;n&&(a=t),i.forEach(l=>{l.classList.remove(r.slideVisibleClass)}),e.visibleSlidesIndexes=[],e.visibleSlides=[];let o=r.spaceBetween;typeof o=="string"&&o.indexOf("%")>=0?o=parseFloat(o.replace("%",""))/100*e.size:typeof o=="string"&&(o=parseFloat(o));for(let l=0;l<i.length;l+=1){const u=i[l];let f=u.swiperSlideOffset;r.cssMode&&r.centeredSlides&&(f-=i[0].swiperSlideOffset);const c=(a+(r.centeredSlides?e.minTranslate():0)-f)/(u.swiperSlideSize+o),p=(a-s[0]+(r.centeredSlides?e.minTranslate():0)-f)/(u.swiperSlideSize+o),d=-(a-f),_=d+e.slidesSizesGrid[l];(d>=0&&d<e.size-1||_>1&&_<=e.size||d<=0&&_>=e.size)&&(e.visibleSlides.push(u),e.visibleSlidesIndexes.push(l),i[l].classList.add(r.slideVisibleClass)),u.progress=n?-c:c,u.originalProgress=n?-p:p}}function pg(t){const e=this;if(typeof t>"u"){const f=e.rtlTranslate?-1:1;t=e&&e.translate&&e.translate*f||0}const r=e.params,i=e.maxTranslate()-e.minTranslate();let{progress:n,isBeginning:s,isEnd:a,progressLoop:o}=e;const l=s,u=a;if(i===0)n=0,s=!0,a=!0;else{n=(t-e.minTranslate())/i;const f=Math.abs(t-e.minTranslate())<1,c=Math.abs(t-e.maxTranslate())<1;s=f||n<=0,a=c||n>=1,f&&(n=0),c&&(n=1)}if(r.loop){const f=e.getSlideIndexByData(0),c=e.getSlideIndexByData(e.slides.length-1),p=e.slidesGrid[f],d=e.slidesGrid[c],_=e.slidesGrid[e.slidesGrid.length-1],h=Math.abs(t);h>=p?o=(h-p)/_:o=(h+_-d)/_,o>1&&(o-=1)}Object.assign(e,{progress:n,progressLoop:o,isBeginning:s,isEnd:a}),(r.watchSlidesProgress||r.centeredSlides&&r.autoHeight)&&e.updateSlidesProgress(t),s&&!l&&e.emit("reachBeginning toEdge"),a&&!u&&e.emit("reachEnd toEdge"),(l&&!s||u&&!a)&&e.emit("fromEdge"),e.emit("progress",n)}function hg(){const t=this,{slides:e,params:r,slidesEl:i,activeIndex:n}=t,s=t.virtual&&r.virtual.enabled,a=l=>Cr(i,`.${r.slideClass}${l}, swiper-slide${l}`)[0];e.forEach(l=>{l.classList.remove(r.slideActiveClass,r.slideNextClass,r.slidePrevClass)});let o;if(s)if(r.loop){let l=n-t.virtual.slidesBefore;l<0&&(l=t.virtual.slides.length+l),l>=t.virtual.slides.length&&(l-=t.virtual.slides.length),o=a(`[data-swiper-slide-index="${l}"]`)}else o=a(`[data-swiper-slide-index="${n}"]`);else o=e[n];if(o){o.classList.add(r.slideActiveClass);let l=Qh(o,`.${r.slideClass}, swiper-slide`)[0];r.loop&&!l&&(l=e[0]),l&&l.classList.add(r.slideNextClass);let u=Zh(o,`.${r.slideClass}, swiper-slide`)[0];r.loop&&!u===0&&(u=e[e.length-1]),u&&u.classList.add(r.slidePrevClass)}t.emitSlidesClasses()}const Ds=(t,e)=>{if(!t||t.destroyed||!t.params)return;const r=()=>t.isElement?"swiper-slide":`.${t.params.slideClass}`,i=e.closest(r());if(i){const n=i.querySelector(`.${t.params.lazyPreloaderClass}`);n&&n.remove()}},Pa=(t,e)=>{if(!t.slides[e])return;const r=t.slides[e].querySelector('[loading="lazy"]');r&&r.removeAttribute("loading")},fo=t=>{if(!t||t.destroyed||!t.params)return;let e=t.params.lazyPreloadPrevNext;const r=t.slides.length;if(!r||!e||e<0)return;e=Math.min(e,r);const i=t.params.slidesPerView==="auto"?t.slidesPerViewDynamic():Math.ceil(t.params.slidesPerView),n=t.activeIndex;if(t.params.grid&&t.params.grid.rows>1){const a=n,o=[a-e];o.push(...Array.from({length:e}).map((l,u)=>a+i+u)),t.slides.forEach((l,u)=>{o.includes(l.column)&&Pa(t,u)});return}const s=n+i-1;if(t.params.rewind||t.params.loop)for(let a=n-e;a<=s+e;a+=1){const o=(a%r+r)%r;(o<n||o>s)&&Pa(t,o)}else for(let a=Math.max(n-e,0);a<=Math.min(s+e,r-1);a+=1)a!==n&&(a>s||a<n)&&Pa(t,a)};function gg(t){const{slidesGrid:e,params:r}=t,i=t.rtlTranslate?t.translate:-t.translate;let n;for(let s=0;s<e.length;s+=1)typeof e[s+1]<"u"?i>=e[s]&&i<e[s+1]-(e[s+1]-e[s])/2?n=s:i>=e[s]&&i<e[s+1]&&(n=s+1):i>=e[s]&&(n=s);return r.normalizeSlideIndex&&(n<0||typeof n>"u")&&(n=0),n}function _g(t){const e=this,r=e.rtlTranslate?e.translate:-e.translate,{snapGrid:i,params:n,activeIndex:s,realIndex:a,snapIndex:o}=e;let l=t,u;const f=p=>{let d=p-e.virtual.slidesBefore;return d<0&&(d=e.virtual.slides.length+d),d>=e.virtual.slides.length&&(d-=e.virtual.slides.length),d};if(typeof l>"u"&&(l=gg(e)),i.indexOf(r)>=0)u=i.indexOf(r);else{const p=Math.min(n.slidesPerGroupSkip,l);u=p+Math.floor((l-p)/n.slidesPerGroup)}if(u>=i.length&&(u=i.length-1),l===s){u!==o&&(e.snapIndex=u,e.emit("snapIndexChange")),e.params.loop&&e.virtual&&e.params.virtual.enabled&&(e.realIndex=f(l));return}let c;e.virtual&&n.virtual.enabled&&n.loop?c=f(l):e.slides[l]?c=parseInt(e.slides[l].getAttribute("data-swiper-slide-index")||l,10):c=l,Object.assign(e,{previousSnapIndex:o,snapIndex:u,previousRealIndex:a,realIndex:c,previousIndex:s,activeIndex:l}),e.initialized&&fo(e),e.emit("activeIndexChange"),e.emit("snapIndexChange"),a!==c&&e.emit("realIndexChange"),(e.initialized||e.params.runCallbacksOnInit)&&e.emit("slideChange")}function mg(t){const e=this,r=e.params,i=t.closest(`.${r.slideClass}, swiper-slide`);let n=!1,s;if(i){for(let a=0;a<e.slides.length;a+=1)if(e.slides[a]===i){n=!0,s=a;break}}if(i&&n)e.clickedSlide=i,e.virtual&&e.params.virtual.enabled?e.clickedIndex=parseInt(i.getAttribute("data-swiper-slide-index"),10):e.clickedIndex=s;else{e.clickedSlide=void 0,e.clickedIndex=void 0;return}r.slideToClickedSlide&&e.clickedIndex!==void 0&&e.clickedIndex!==e.activeIndex&&e.slideToClickedSlide()}const vg={updateSize:lg,updateSlides:ug,updateAutoHeight:fg,updateSlidesOffset:cg,updateSlidesProgress:dg,updateProgress:pg,updateSlidesClasses:hg,updateActiveIndex:_g,updateClickedSlide:mg};function yg(t=this.isHorizontal()?"x":"y"){const e=this,{params:r,rtlTranslate:i,translate:n,wrapperEl:s}=e;if(r.virtualTranslate)return i?-n:n;if(r.cssMode)return n;let a=Kh(s,t);return a+=e.cssOverflowAdjustment(),i&&(a=-a),a||0}function xg(t,e){const r=this,{rtlTranslate:i,params:n,wrapperEl:s,progress:a}=r;let o=0,l=0;const u=0;r.isHorizontal()?o=i?-t:t:l=t,n.roundLengths&&(o=Math.floor(o),l=Math.floor(l)),r.previousTranslate=r.translate,r.translate=r.isHorizontal()?o:l,n.cssMode?s[r.isHorizontal()?"scrollLeft":"scrollTop"]=r.isHorizontal()?-o:-l:n.virtualTranslate||(r.isHorizontal()?o-=r.cssOverflowAdjustment():l-=r.cssOverflowAdjustment(),s.style.transform=`translate3d(${o}px, ${l}px, ${u}px)`);let f;const c=r.maxTranslate()-r.minTranslate();c===0?f=0:f=(t-r.minTranslate())/c,f!==a&&r.updateProgress(t),r.emit("setTranslate",r.translate,e)}function bg(){return-this.snapGrid[0]}function Sg(){return-this.snapGrid[this.snapGrid.length-1]}function wg(t=0,e=this.params.speed,r=!0,i=!0,n){const s=this,{params:a,wrapperEl:o}=s;if(s.animating&&a.preventInteractionOnTransition)return!1;const l=s.minTranslate(),u=s.maxTranslate();let f;if(i&&t>l?f=l:i&&t<u?f=u:f=t,s.updateProgress(f),a.cssMode){const c=s.isHorizontal();if(e===0)o[c?"scrollLeft":"scrollTop"]=-f;else{if(!s.support.smoothScroll)return ec({swiper:s,targetPosition:-f,side:c?"left":"top"}),!0;o.scrollTo({[c?"left":"top"]:-f,behavior:"smooth"})}return!0}return e===0?(s.setTransition(0),s.setTranslate(f),r&&(s.emit("beforeTransitionStart",e,n),s.emit("transitionEnd"))):(s.setTransition(e),s.setTranslate(f),r&&(s.emit("beforeTransitionStart",e,n),s.emit("transitionStart")),s.animating||(s.animating=!0,s.onTranslateToWrapperTransitionEnd||(s.onTranslateToWrapperTransitionEnd=function(p){!s||s.destroyed||p.target===this&&(s.wrapperEl.removeEventListener("transitionend",s.onTranslateToWrapperTransitionEnd),s.onTranslateToWrapperTransitionEnd=null,delete s.onTranslateToWrapperTransitionEnd,r&&s.emit("transitionEnd"))}),s.wrapperEl.addEventListener("transitionend",s.onTranslateToWrapperTransitionEnd))),!0}const Tg={getTranslate:yg,setTranslate:xg,minTranslate:bg,maxTranslate:Sg,translateTo:wg};function Cg(t,e){const r=this;r.params.cssMode||(r.wrapperEl.style.transitionDuration=`${t}ms`),r.emit("setTransition",t,e)}function nc({swiper:t,runCallbacks:e,direction:r,step:i}){const{activeIndex:n,previousIndex:s}=t;let a=r;if(a||(n>s?a="next":n<s?a="prev":a="reset"),t.emit(`transition${i}`),e&&n!==s){if(a==="reset"){t.emit(`slideResetTransition${i}`);return}t.emit(`slideChangeTransition${i}`),a==="next"?t.emit(`slideNextTransition${i}`):t.emit(`slidePrevTransition${i}`)}}function Eg(t=!0,e){const r=this,{params:i}=r;i.cssMode||(i.autoHeight&&r.updateAutoHeight(),nc({swiper:r,runCallbacks:t,direction:e,step:"Start"}))}function Pg(t=!0,e){const r=this,{params:i}=r;r.animating=!1,!i.cssMode&&(r.setTransition(0),nc({swiper:r,runCallbacks:t,direction:e,step:"End"}))}const Mg={setTransition:Cg,transitionStart:Eg,transitionEnd:Pg};function Og(t=0,e=this.params.speed,r=!0,i,n){typeof t=="string"&&(t=parseInt(t,10));const s=this;let a=t;a<0&&(a=0);const{params:o,snapGrid:l,slidesGrid:u,previousIndex:f,activeIndex:c,rtlTranslate:p,wrapperEl:d,enabled:_}=s;if(s.animating&&o.preventInteractionOnTransition||!_&&!i&&!n)return!1;const h=Math.min(s.params.slidesPerGroupSkip,a);let m=h+Math.floor((a-h)/s.params.slidesPerGroup);m>=l.length&&(m=l.length-1);const g=-l[m];if(o.normalizeSlideIndex)for(let x=0;x<u.length;x+=1){const y=-Math.floor(g*100),b=Math.floor(u[x]*100),w=Math.floor(u[x+1]*100);typeof u[x+1]<"u"?y>=b&&y<w-(w-b)/2?a=x:y>=b&&y<w&&(a=x+1):y>=b&&(a=x)}if(s.initialized&&a!==c&&(!s.allowSlideNext&&(p?g>s.translate&&g>s.minTranslate():g<s.translate&&g<s.minTranslate())||!s.allowSlidePrev&&g>s.translate&&g>s.maxTranslate()&&(c||0)!==a))return!1;a!==(f||0)&&r&&s.emit("beforeSlideChangeStart"),s.updateProgress(g);let v;if(a>c?v="next":a<c?v="prev":v="reset",p&&-g===s.translate||!p&&g===s.translate)return s.updateActiveIndex(a),o.autoHeight&&s.updateAutoHeight(),s.updateSlidesClasses(),o.effect!=="slide"&&s.setTranslate(g),v!=="reset"&&(s.transitionStart(r,v),s.transitionEnd(r,v)),!1;if(o.cssMode){const x=s.isHorizontal(),y=p?g:-g;if(e===0){const b=s.virtual&&s.params.virtual.enabled;b&&(s.wrapperEl.style.scrollSnapType="none",s._immediateVirtual=!0),b&&!s._cssModeVirtualInitialSet&&s.params.initialSlide>0?(s._cssModeVirtualInitialSet=!0,requestAnimationFrame(()=>{d[x?"scrollLeft":"scrollTop"]=y})):d[x?"scrollLeft":"scrollTop"]=y,b&&requestAnimationFrame(()=>{s.wrapperEl.style.scrollSnapType="",s._immediateVirtual=!1})}else{if(!s.support.smoothScroll)return ec({swiper:s,targetPosition:y,side:x?"left":"top"}),!0;d.scrollTo({[x?"left":"top"]:y,behavior:"smooth"})}return!0}return s.setTransition(e),s.setTranslate(g),s.updateActiveIndex(a),s.updateSlidesClasses(),s.emit("beforeTransitionStart",e,i),s.transitionStart(r,v),e===0?s.transitionEnd(r,v):s.animating||(s.animating=!0,s.onSlideToWrapperTransitionEnd||(s.onSlideToWrapperTransitionEnd=function(y){!s||s.destroyed||y.target===this&&(s.wrapperEl.removeEventListener("transitionend",s.onSlideToWrapperTransitionEnd),s.onSlideToWrapperTransitionEnd=null,delete s.onSlideToWrapperTransitionEnd,s.transitionEnd(r,v))}),s.wrapperEl.addEventListener("transitionend",s.onSlideToWrapperTransitionEnd)),!0}function Ag(t=0,e=this.params.speed,r=!0,i){typeof t=="string"&&(t=parseInt(t,10));const n=this;let s=t;return n.params.loop&&(n.virtual&&n.params.virtual.enabled?s=s+n.virtual.slidesBefore:s=n.getSlideIndexByData(s)),n.slideTo(s,e,r,i)}function kg(t=this.params.speed,e=!0,r){const i=this,{enabled:n,params:s,animating:a}=i;if(!n)return i;let o=s.slidesPerGroup;s.slidesPerView==="auto"&&s.slidesPerGroup===1&&s.slidesPerGroupAuto&&(o=Math.max(i.slidesPerViewDynamic("current",!0),1));const l=i.activeIndex<s.slidesPerGroupSkip?1:o,u=i.virtual&&s.virtual.enabled;if(s.loop){if(a&&!u&&s.loopPreventsSliding)return!1;i.loopFix({direction:"next"}),i._clientLeft=i.wrapperEl.clientLeft}return s.rewind&&i.isEnd?i.slideTo(0,t,e,r):i.slideTo(i.activeIndex+l,t,e,r)}function Lg(t=this.params.speed,e=!0,r){const i=this,{params:n,snapGrid:s,slidesGrid:a,rtlTranslate:o,enabled:l,animating:u}=i;if(!l)return i;const f=i.virtual&&n.virtual.enabled;if(n.loop){if(u&&!f&&n.loopPreventsSliding)return!1;i.loopFix({direction:"prev"}),i._clientLeft=i.wrapperEl.clientLeft}const c=o?i.translate:-i.translate;function p(g){return g<0?-Math.floor(Math.abs(g)):Math.floor(g)}const d=p(c),_=s.map(g=>p(g));let h=s[_.indexOf(d)-1];if(typeof h>"u"&&n.cssMode){let g;s.forEach((v,x)=>{d>=v&&(g=x)}),typeof g<"u"&&(h=s[g>0?g-1:g])}let m=0;if(typeof h<"u"&&(m=a.indexOf(h),m<0&&(m=i.activeIndex-1),n.slidesPerView==="auto"&&n.slidesPerGroup===1&&n.slidesPerGroupAuto&&(m=m-i.slidesPerViewDynamic("previous",!0)+1,m=Math.max(m,0))),n.rewind&&i.isBeginning){const g=i.params.virtual&&i.params.virtual.enabled&&i.virtual?i.virtual.slides.length-1:i.slides.length-1;return i.slideTo(g,t,e,r)}return i.slideTo(m,t,e,r)}function Dg(t=this.params.speed,e=!0,r){const i=this;return i.slideTo(i.activeIndex,t,e,r)}function Ig(t=this.params.speed,e=!0,r,i=.5){const n=this;let s=n.activeIndex;const a=Math.min(n.params.slidesPerGroupSkip,s),o=a+Math.floor((s-a)/n.params.slidesPerGroup),l=n.rtlTranslate?n.translate:-n.translate;if(l>=n.snapGrid[o]){const u=n.snapGrid[o],f=n.snapGrid[o+1];l-u>(f-u)*i&&(s+=n.params.slidesPerGroup)}else{const u=n.snapGrid[o-1],f=n.snapGrid[o];l-u<=(f-u)*i&&(s-=n.params.slidesPerGroup)}return s=Math.max(s,0),s=Math.min(s,n.slidesGrid.length-1),n.slideTo(s,t,e,r)}function Rg(){const t=this,{params:e,slidesEl:r}=t,i=e.slidesPerView==="auto"?t.slidesPerViewDynamic():e.slidesPerView;let n=t.clickedIndex,s;const a=t.isElement?"swiper-slide":`.${e.slideClass}`;if(e.loop){if(t.animating)return;s=parseInt(t.clickedSlide.getAttribute("data-swiper-slide-index"),10),e.centeredSlides?n<t.loopedSlides-i/2||n>t.slides.length-t.loopedSlides+i/2?(t.loopFix(),n=t.getSlideIndex(Cr(r,`${a}[data-swiper-slide-index="${s}"]`)[0]),lo(()=>{t.slideTo(n)})):t.slideTo(n):n>t.slides.length-i?(t.loopFix(),n=t.getSlideIndex(Cr(r,`${a}[data-swiper-slide-index="${s}"]`)[0]),lo(()=>{t.slideTo(n)})):t.slideTo(n)}else t.slideTo(n)}const zg={slideTo:Og,slideToLoop:Ag,slideNext:kg,slidePrev:Lg,slideReset:Dg,slideToClosest:Ig,slideToClickedSlide:Rg};function Fg(t){const e=this,{params:r,slidesEl:i}=e;if(!r.loop||e.virtual&&e.params.virtual.enabled)return;Cr(i,`.${r.slideClass}, swiper-slide`).forEach((s,a)=>{s.setAttribute("data-swiper-slide-index",a)}),e.loopFix({slideRealIndex:t,direction:r.centeredSlides?void 0:"next"})}function $g({slideRealIndex:t,slideTo:e=!0,direction:r,setTranslate:i,activeSlideIndex:n,byController:s,byMousewheel:a}={}){const o=this;if(!o.params.loop)return;o.emit("beforeLoopFix");const{slides:l,allowSlidePrev:u,allowSlideNext:f,slidesEl:c,params:p}=o;if(o.allowSlidePrev=!0,o.allowSlideNext=!0,o.virtual&&p.virtual.enabled){e&&(!p.centeredSlides&&o.snapIndex===0?o.slideTo(o.virtual.slides.length,0,!1,!0):p.centeredSlides&&o.snapIndex<p.slidesPerView?o.slideTo(o.virtual.slides.length+o.snapIndex,0,!1,!0):o.snapIndex===o.snapGrid.length-1&&o.slideTo(o.virtual.slidesBefore,0,!1,!0)),o.allowSlidePrev=u,o.allowSlideNext=f,o.emit("loopFix");return}const d=p.slidesPerView==="auto"?o.slidesPerViewDynamic():Math.ceil(parseFloat(p.slidesPerView,10));let _=p.loopedSlides||d;_%p.slidesPerGroup!==0&&(_+=p.slidesPerGroup-_%p.slidesPerGroup),o.loopedSlides=_;const h=[],m=[];let g=o.activeIndex;typeof n>"u"?n=o.getSlideIndex(o.slides.filter(w=>w.classList.contains(p.slideActiveClass))[0]):g=n;const v=r==="next"||!r,x=r==="prev"||!r;let y=0,b=0;if(n<_){y=Math.max(_-n,p.slidesPerGroup);for(let w=0;w<_-n;w+=1){const C=w-Math.floor(w/l.length)*l.length;h.push(l.length-C-1)}}else if(n>o.slides.length-_*2){b=Math.max(n-(o.slides.length-_*2),p.slidesPerGroup);for(let w=0;w<b;w+=1){const C=w-Math.floor(w/l.length)*l.length;m.push(C)}}if(x&&h.forEach(w=>{o.slides[w].swiperLoopMoveDOM=!0,c.prepend(o.slides[w]),o.slides[w].swiperLoopMoveDOM=!1}),v&&m.forEach(w=>{o.slides[w].swiperLoopMoveDOM=!0,c.append(o.slides[w]),o.slides[w].swiperLoopMoveDOM=!1}),o.recalcSlides(),p.slidesPerView==="auto"&&o.updateSlides(),p.watchSlidesProgress&&o.updateSlidesOffset(),e){if(h.length>0&&x)if(typeof t>"u"){const w=o.slidesGrid[g],O=o.slidesGrid[g+y]-w;a?o.setTranslate(o.translate-O):(o.slideTo(g+y,0,!1,!0),i&&(o.touches[o.isHorizontal()?"startX":"startY"]+=O))}else i&&o.slideToLoop(t,0,!1,!0);else if(m.length>0&&v)if(typeof t>"u"){const w=o.slidesGrid[g],O=o.slidesGrid[g-b]-w;a?o.setTranslate(o.translate-O):(o.slideTo(g-b,0,!1,!0),i&&(o.touches[o.isHorizontal()?"startX":"startY"]+=O))}else o.slideToLoop(t,0,!1,!0)}if(o.allowSlidePrev=u,o.allowSlideNext=f,o.controller&&o.controller.control&&!s){const w={slideRealIndex:t,slideTo:!1,direction:r,setTranslate:i,activeSlideIndex:n,byController:!0};Array.isArray(o.controller.control)?o.controller.control.forEach(C=>{!C.destroyed&&C.params.loop&&C.loopFix(w)}):o.controller.control instanceof o.constructor&&o.controller.control.params.loop&&o.controller.control.loopFix(w)}o.emit("loopFix")}function Bg(){const t=this,{params:e,slidesEl:r}=t;if(!e.loop||t.virtual&&t.params.virtual.enabled)return;t.recalcSlides();const i=[];t.slides.forEach(n=>{const s=typeof n.swiperSlideIndex>"u"?n.getAttribute("data-swiper-slide-index")*1:n.swiperSlideIndex;i[s]=n}),t.slides.forEach(n=>{n.removeAttribute("data-swiper-slide-index")}),i.forEach(n=>{r.append(n)}),t.recalcSlides(),t.slideTo(t.realIndex,0)}const Ng={loopCreate:Fg,loopFix:$g,loopDestroy:Bg};function Vg(t){const e=this;if(!e.params.simulateTouch||e.params.watchOverflow&&e.isLocked||e.params.cssMode)return;const r=e.params.touchEventsTarget==="container"?e.el:e.wrapperEl;e.isElement&&(e.__preventObserver__=!0),r.style.cursor="move",r.style.cursor=t?"grabbing":"grab",e.isElement&&requestAnimationFrame(()=>{e.__preventObserver__=!1})}function Gg(){const t=this;t.params.watchOverflow&&t.isLocked||t.params.cssMode||(t.isElement&&(t.__preventObserver__=!0),t[t.params.touchEventsTarget==="container"?"el":"wrapperEl"].style.cursor="",t.isElement&&requestAnimationFrame(()=>{t.__preventObserver__=!1}))}const Hg={setGrabCursor:Vg,unsetGrabCursor:Gg};function Yg(t,e=this){function r(i){if(!i||i===Mr()||i===Zt())return null;i.assignedSlot&&(i=i.assignedSlot);const n=i.closest(t);return!n&&!i.getRootNode?null:n||r(i.getRootNode().host)}return r(e)}function jg(t){const e=this,r=Mr(),i=Zt(),n=e.touchEventsData;n.evCache.push(t);const{params:s,touches:a,enabled:o}=e;if(!o||!s.simulateTouch&&t.pointerType==="mouse"||e.animating&&s.preventInteractionOnTransition)return;!e.animating&&s.cssMode&&s.loop&&e.loopFix();let l=t;l.originalEvent&&(l=l.originalEvent);let u=l.target;if(s.touchEventsTarget==="wrapper"&&!e.wrapperEl.contains(u)||"which"in l&&l.which===3||"button"in l&&l.button>0||n.isTouched&&n.isMoved)return;const f=!!s.noSwipingClass&&s.noSwipingClass!=="",c=t.composedPath?t.composedPath():t.path;f&&l.target&&l.target.shadowRoot&&c&&(u=c[0]);const p=s.noSwipingSelector?s.noSwipingSelector:`.${s.noSwipingClass}`,d=!!(l.target&&l.target.shadowRoot);if(s.noSwiping&&(d?Yg(p,u):u.closest(p))){e.allowClick=!0;return}if(s.swipeHandler&&!u.closest(s.swipeHandler))return;a.currentX=l.pageX,a.currentY=l.pageY;const _=a.currentX,h=a.currentY,m=s.edgeSwipeDetection||s.iOSEdgeSwipeDetection,g=s.edgeSwipeThreshold||s.iOSEdgeSwipeThreshold;if(m&&(_<=g||_>=i.innerWidth-g))if(m==="prevent")t.preventDefault();else return;Object.assign(n,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),a.startX=_,a.startY=h,n.touchStartTime=qs(),e.allowClick=!0,e.updateSize(),e.swipeDirection=void 0,s.threshold>0&&(n.allowThresholdMove=!1);let v=!0;u.matches(n.focusableElements)&&(v=!1,u.nodeName==="SELECT"&&(n.isTouched=!1)),r.activeElement&&r.activeElement.matches(n.focusableElements)&&r.activeElement!==u&&r.activeElement.blur();const x=v&&e.allowTouchMove&&s.touchStartPreventDefault;(s.touchStartForcePreventDefault||x)&&!u.isContentEditable&&l.preventDefault(),s.freeMode&&s.freeMode.enabled&&e.freeMode&&e.animating&&!s.cssMode&&e.freeMode.onTouchStart(),e.emit("touchStart",l)}function Wg(t){const e=Mr(),r=this,i=r.touchEventsData,{params:n,touches:s,rtlTranslate:a,enabled:o}=r;if(!o||!n.simulateTouch&&t.pointerType==="mouse")return;let l=t;if(l.originalEvent&&(l=l.originalEvent),!i.isTouched){i.startMoving&&i.isScrolling&&r.emit("touchMoveOpposite",l);return}const u=i.evCache.findIndex(w=>w.pointerId===l.pointerId);u>=0&&(i.evCache[u]=l);const f=i.evCache.length>1?i.evCache[0]:l,c=f.pageX,p=f.pageY;if(l.preventedByNestedSwiper){s.startX=c,s.startY=p;return}if(!r.allowTouchMove){l.target.matches(i.focusableElements)||(r.allowClick=!1),i.isTouched&&(Object.assign(s,{startX:c,startY:p,prevX:r.touches.currentX,prevY:r.touches.currentY,currentX:c,currentY:p}),i.touchStartTime=qs());return}if(n.touchReleaseOnEdges&&!n.loop){if(r.isVertical()){if(p<s.startY&&r.translate<=r.maxTranslate()||p>s.startY&&r.translate>=r.minTranslate()){i.isTouched=!1,i.isMoved=!1;return}}else if(c<s.startX&&r.translate<=r.maxTranslate()||c>s.startX&&r.translate>=r.minTranslate())return}if(e.activeElement&&l.target===e.activeElement&&l.target.matches(i.focusableElements)){i.isMoved=!0,r.allowClick=!1;return}if(i.allowTouchCallbacks&&r.emit("touchMove",l),l.targetTouches&&l.targetTouches.length>1)return;s.currentX=c,s.currentY=p;const d=s.currentX-s.startX,_=s.currentY-s.startY;if(r.params.threshold&&Math.sqrt(d**2+_**2)<r.params.threshold)return;if(typeof i.isScrolling>"u"){let w;r.isHorizontal()&&s.currentY===s.startY||r.isVertical()&&s.currentX===s.startX?i.isScrolling=!1:d*d+_*_>=25&&(w=Math.atan2(Math.abs(_),Math.abs(d))*180/Math.PI,i.isScrolling=r.isHorizontal()?w>n.touchAngle:90-w>n.touchAngle)}if(i.isScrolling&&r.emit("touchMoveOpposite",l),typeof i.startMoving>"u"&&(s.currentX!==s.startX||s.currentY!==s.startY)&&(i.startMoving=!0),i.isScrolling||r.zoom&&r.params.zoom&&r.params.zoom.enabled&&i.evCache.length>1){i.isTouched=!1;return}if(!i.startMoving)return;r.allowClick=!1,!n.cssMode&&l.cancelable&&l.preventDefault(),n.touchMoveStopPropagation&&!n.nested&&l.stopPropagation();let h=r.isHorizontal()?d:_,m=r.isHorizontal()?s.currentX-s.previousX:s.currentY-s.previousY;n.oneWayMovement&&(h=Math.abs(h)*(a?1:-1),m=Math.abs(m)*(a?1:-1)),s.diff=h,h*=n.touchRatio,a&&(h=-h,m=-m);const g=r.touchesDirection;r.swipeDirection=h>0?"prev":"next",r.touchesDirection=m>0?"prev":"next";const v=r.params.loop&&!n.cssMode;if(!i.isMoved){if(v&&r.loopFix({direction:r.swipeDirection}),i.startTranslate=r.getTranslate(),r.setTransition(0),r.animating){const w=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0});r.wrapperEl.dispatchEvent(w)}i.allowMomentumBounce=!1,n.grabCursor&&(r.allowSlideNext===!0||r.allowSlidePrev===!0)&&r.setGrabCursor(!0),r.emit("sliderFirstMove",l)}let x;i.isMoved&&g!==r.touchesDirection&&v&&Math.abs(h)>=1&&(r.loopFix({direction:r.swipeDirection,setTranslate:!0}),x=!0),r.emit("sliderMove",l),i.isMoved=!0,i.currentTranslate=h+i.startTranslate;let y=!0,b=n.resistanceRatio;if(n.touchReleaseOnEdges&&(b=0),h>0?(v&&!x&&i.currentTranslate>(n.centeredSlides?r.minTranslate()-r.size/2:r.minTranslate())&&r.loopFix({direction:"prev",setTranslate:!0,activeSlideIndex:0}),i.currentTranslate>r.minTranslate()&&(y=!1,n.resistance&&(i.currentTranslate=r.minTranslate()-1+(-r.minTranslate()+i.startTranslate+h)**b))):h<0&&(v&&!x&&i.currentTranslate<(n.centeredSlides?r.maxTranslate()+r.size/2:r.maxTranslate())&&r.loopFix({direction:"next",setTranslate:!0,activeSlideIndex:r.slides.length-(n.slidesPerView==="auto"?r.slidesPerViewDynamic():Math.ceil(parseFloat(n.slidesPerView,10)))}),i.currentTranslate<r.maxTranslate()&&(y=!1,n.resistance&&(i.currentTranslate=r.maxTranslate()+1-(r.maxTranslate()-i.startTranslate-h)**b))),y&&(l.preventedByNestedSwiper=!0),!r.allowSlideNext&&r.swipeDirection==="next"&&i.currentTranslate<i.startTranslate&&(i.currentTranslate=i.startTranslate),!r.allowSlidePrev&&r.swipeDirection==="prev"&&i.currentTranslate>i.startTranslate&&(i.currentTranslate=i.startTranslate),!r.allowSlidePrev&&!r.allowSlideNext&&(i.currentTranslate=i.startTranslate),n.threshold>0)if(Math.abs(h)>n.threshold||i.allowThresholdMove){if(!i.allowThresholdMove){i.allowThresholdMove=!0,s.startX=s.currentX,s.startY=s.currentY,i.currentTranslate=i.startTranslate,s.diff=r.isHorizontal()?s.currentX-s.startX:s.currentY-s.startY;return}}else{i.currentTranslate=i.startTranslate;return}!n.followFinger||n.cssMode||((n.freeMode&&n.freeMode.enabled&&r.freeMode||n.watchSlidesProgress)&&(r.updateActiveIndex(),r.updateSlidesClasses()),n.freeMode&&n.freeMode.enabled&&r.freeMode&&r.freeMode.onTouchMove(),r.updateProgress(i.currentTranslate),r.setTranslate(i.currentTranslate))}function qg(t){const e=this,r=e.touchEventsData,i=r.evCache.findIndex(x=>x.pointerId===t.pointerId);if(i>=0&&r.evCache.splice(i,1),["pointercancel","pointerout","pointerleave"].includes(t.type)&&!(t.type==="pointercancel"&&(e.browser.isSafari||e.browser.isWebView)))return;const{params:n,touches:s,rtlTranslate:a,slidesGrid:o,enabled:l}=e;if(!l||!n.simulateTouch&&t.pointerType==="mouse")return;let u=t;if(u.originalEvent&&(u=u.originalEvent),r.allowTouchCallbacks&&e.emit("touchEnd",u),r.allowTouchCallbacks=!1,!r.isTouched){r.isMoved&&n.grabCursor&&e.setGrabCursor(!1),r.isMoved=!1,r.startMoving=!1;return}n.grabCursor&&r.isMoved&&r.isTouched&&(e.allowSlideNext===!0||e.allowSlidePrev===!0)&&e.setGrabCursor(!1);const f=qs(),c=f-r.touchStartTime;if(e.allowClick){const x=u.path||u.composedPath&&u.composedPath();e.updateClickedSlide(x&&x[0]||u.target),e.emit("tap click",u),c<300&&f-r.lastClickTime<300&&e.emit("doubleTap doubleClick",u)}if(r.lastClickTime=qs(),lo(()=>{e.destroyed||(e.allowClick=!0)}),!r.isTouched||!r.isMoved||!e.swipeDirection||s.diff===0||r.currentTranslate===r.startTranslate){r.isTouched=!1,r.isMoved=!1,r.startMoving=!1;return}r.isTouched=!1,r.isMoved=!1,r.startMoving=!1;let p;if(n.followFinger?p=a?e.translate:-e.translate:p=-r.currentTranslate,n.cssMode)return;if(n.freeMode&&n.freeMode.enabled){e.freeMode.onTouchEnd({currentPos:p});return}let d=0,_=e.slidesSizesGrid[0];for(let x=0;x<o.length;x+=x<n.slidesPerGroupSkip?1:n.slidesPerGroup){const y=x<n.slidesPerGroupSkip-1?1:n.slidesPerGroup;typeof o[x+y]<"u"?p>=o[x]&&p<o[x+y]&&(d=x,_=o[x+y]-o[x]):p>=o[x]&&(d=x,_=o[o.length-1]-o[o.length-2])}let h=null,m=null;n.rewind&&(e.isBeginning?m=n.virtual&&n.virtual.enabled&&e.virtual?e.virtual.slides.length-1:e.slides.length-1:e.isEnd&&(h=0));const g=(p-o[d])/_,v=d<n.slidesPerGroupSkip-1?1:n.slidesPerGroup;if(c>n.longSwipesMs){if(!n.longSwipes){e.slideTo(e.activeIndex);return}e.swipeDirection==="next"&&(g>=n.longSwipesRatio?e.slideTo(n.rewind&&e.isEnd?h:d+v):e.slideTo(d)),e.swipeDirection==="prev"&&(g>1-n.longSwipesRatio?e.slideTo(d+v):m!==null&&g<0&&Math.abs(g)>n.longSwipesRatio?e.slideTo(m):e.slideTo(d))}else{if(!n.shortSwipes){e.slideTo(e.activeIndex);return}e.navigation&&(u.target===e.navigation.nextEl||u.target===e.navigation.prevEl)?u.target===e.navigation.nextEl?e.slideTo(d+v):e.slideTo(d):(e.swipeDirection==="next"&&e.slideTo(h!==null?h:d+v),e.swipeDirection==="prev"&&e.slideTo(m!==null?m:d))}}function Ul(){const t=this,{params:e,el:r}=t;if(r&&r.offsetWidth===0)return;e.breakpoints&&t.setBreakpoint();const{allowSlideNext:i,allowSlidePrev:n,snapGrid:s}=t,a=t.virtual&&t.params.virtual.enabled;t.allowSlideNext=!0,t.allowSlidePrev=!0,t.updateSize(),t.updateSlides(),t.updateSlidesClasses();const o=a&&e.loop;(e.slidesPerView==="auto"||e.slidesPerView>1)&&t.isEnd&&!t.isBeginning&&!t.params.centeredSlides&&!o?t.slideTo(t.slides.length-1,0,!1,!0):t.params.loop&&!a?t.slideToLoop(t.realIndex,0,!1,!0):t.slideTo(t.activeIndex,0,!1,!0),t.autoplay&&t.autoplay.running&&t.autoplay.paused&&(clearTimeout(t.autoplay.resizeTimeout),t.autoplay.resizeTimeout=setTimeout(()=>{t.autoplay&&t.autoplay.running&&t.autoplay.paused&&t.autoplay.resume()},500)),t.allowSlidePrev=n,t.allowSlideNext=i,t.params.watchOverflow&&s!==t.snapGrid&&t.checkOverflow()}function Xg(t){const e=this;e.enabled&&(e.allowClick||(e.params.preventClicks&&t.preventDefault(),e.params.preventClicksPropagation&&e.animating&&(t.stopPropagation(),t.stopImmediatePropagation())))}function Ug(){const t=this,{wrapperEl:e,rtlTranslate:r,enabled:i}=t;if(!i)return;t.previousTranslate=t.translate,t.isHorizontal()?t.translate=-e.scrollLeft:t.translate=-e.scrollTop,t.translate===0&&(t.translate=0),t.updateActiveIndex(),t.updateSlidesClasses();let n;const s=t.maxTranslate()-t.minTranslate();s===0?n=0:n=(t.translate-t.minTranslate())/s,n!==t.progress&&t.updateProgress(r?-t.translate:t.translate),t.emit("setTranslate",t.translate,!1)}function Kg(t){const e=this;Ds(e,t.target),!(e.params.cssMode||e.params.slidesPerView!=="auto"&&!e.params.autoHeight)&&e.update()}let Kl=!1;function Jg(){}const sc=(t,e)=>{const r=Mr(),{params:i,el:n,wrapperEl:s,device:a}=t,o=!!i.nested,l=e==="on"?"addEventListener":"removeEventListener",u=e;n[l]("pointerdown",t.onTouchStart,{passive:!1}),r[l]("pointermove",t.onTouchMove,{passive:!1,capture:o}),r[l]("pointerup",t.onTouchEnd,{passive:!0}),r[l]("pointercancel",t.onTouchEnd,{passive:!0}),r[l]("pointerout",t.onTouchEnd,{passive:!0}),r[l]("pointerleave",t.onTouchEnd,{passive:!0}),(i.preventClicks||i.preventClicksPropagation)&&n[l]("click",t.onClick,!0),i.cssMode&&s[l]("scroll",t.onScroll),i.updateOnWindowResize?t[u](a.ios||a.android?"resize orientationchange observerUpdate":"resize observerUpdate",Ul,!0):t[u]("observerUpdate",Ul,!0),n[l]("load",t.onLoad,{capture:!0})};function Zg(){const t=this,e=Mr(),{params:r}=t;t.onTouchStart=jg.bind(t),t.onTouchMove=Wg.bind(t),t.onTouchEnd=qg.bind(t),r.cssMode&&(t.onScroll=Ug.bind(t)),t.onClick=Xg.bind(t),t.onLoad=Kg.bind(t),Kl||(e.addEventListener("touchstart",Jg),Kl=!0),sc(t,"on")}function Qg(){sc(this,"off")}const e_={attachEvents:Zg,detachEvents:Qg},Jl=(t,e)=>t.grid&&e.grid&&e.grid.rows>1;function t_(){const t=this,{realIndex:e,initialized:r,params:i,el:n}=t,s=i.breakpoints;if(!s||s&&Object.keys(s).length===0)return;const a=t.getBreakpoint(s,t.params.breakpointsBase,t.el);if(!a||t.currentBreakpoint===a)return;const l=(a in s?s[a]:void 0)||t.originalParams,u=Jl(t,i),f=Jl(t,l),c=i.enabled;u&&!f?(n.classList.remove(`${i.containerModifierClass}grid`,`${i.containerModifierClass}grid-column`),t.emitContainerClasses()):!u&&f&&(n.classList.add(`${i.containerModifierClass}grid`),(l.grid.fill&&l.grid.fill==="column"||!l.grid.fill&&i.grid.fill==="column")&&n.classList.add(`${i.containerModifierClass}grid-column`),t.emitContainerClasses()),["navigation","pagination","scrollbar"].forEach(h=>{if(typeof l[h]>"u")return;const m=i[h]&&i[h].enabled,g=l[h]&&l[h].enabled;m&&!g&&t[h].disable(),!m&&g&&t[h].enable()});const p=l.direction&&l.direction!==i.direction,d=i.loop&&(l.slidesPerView!==i.slidesPerView||p);p&&r&&t.changeDirection(),Ht(t.params,l);const _=t.params.enabled;Object.assign(t,{allowTouchMove:t.params.allowTouchMove,allowSlideNext:t.params.allowSlideNext,allowSlidePrev:t.params.allowSlidePrev}),c&&!_?t.disable():!c&&_&&t.enable(),t.currentBreakpoint=a,t.emit("_beforeBreakpoint",l),d&&r&&(t.loopDestroy(),t.loopCreate(e),t.updateSlides()),t.emit("breakpoint",l)}function r_(t,e="window",r){if(!t||e==="container"&&!r)return;let i=!1;const n=Zt(),s=e==="window"?n.innerHeight:r.clientHeight,a=Object.keys(t).map(o=>{if(typeof o=="string"&&o.indexOf("@")===0){const l=parseFloat(o.substr(1));return{value:s*l,point:o}}return{value:o,point:o}});a.sort((o,l)=>parseInt(o.value,10)-parseInt(l.value,10));for(let o=0;o<a.length;o+=1){const{point:l,value:u}=a[o];e==="window"?n.matchMedia(`(min-width: ${u}px)`).matches&&(i=l):u<=r.clientWidth&&(i=l)}return i||"max"}const i_={setBreakpoint:t_,getBreakpoint:r_};function n_(t,e){const r=[];return t.forEach(i=>{typeof i=="object"?Object.keys(i).forEach(n=>{i[n]&&r.push(e+n)}):typeof i=="string"&&r.push(e+i)}),r}function s_(){const t=this,{classNames:e,params:r,rtl:i,el:n,device:s}=t,a=n_(["initialized",r.direction,{"free-mode":t.params.freeMode&&r.freeMode.enabled},{autoheight:r.autoHeight},{rtl:i},{grid:r.grid&&r.grid.rows>1},{"grid-column":r.grid&&r.grid.rows>1&&r.grid.fill==="column"},{android:s.android},{ios:s.ios},{"css-mode":r.cssMode},{centered:r.cssMode&&r.centeredSlides},{"watch-progress":r.watchSlidesProgress}],r.containerModifierClass);e.push(...a),n.classList.add(...e),t.emitContainerClasses()}function a_(){const t=this,{el:e,classNames:r}=t;e.classList.remove(...r),t.emitContainerClasses()}const o_={addClasses:s_,removeClasses:a_};function l_(){const t=this,{isLocked:e,params:r}=t,{slidesOffsetBefore:i}=r;if(i){const n=t.slides.length-1,s=t.slidesGrid[n]+t.slidesSizesGrid[n]+i*2;t.isLocked=t.size>s}else t.isLocked=t.snapGrid.length===1;r.allowSlideNext===!0&&(t.allowSlideNext=!t.isLocked),r.allowSlidePrev===!0&&(t.allowSlidePrev=!t.isLocked),e&&e!==t.isLocked&&(t.isEnd=!1),e!==t.isLocked&&t.emit(t.isLocked?"lock":"unlock")}const u_={checkOverflow:l_},Zl={init:!0,direction:"horizontal",oneWayMovement:!1,touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:5,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,loop:!1,loopedSlides:null,loopPreventsSliding:!0,rewind:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,maxBackfaceHiddenSlides:10,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideActiveClass:"swiper-slide-active",slideVisibleClass:"swiper-slide-visible",slideNextClass:"swiper-slide-next",slidePrevClass:"swiper-slide-prev",wrapperClass:"swiper-wrapper",lazyPreloaderClass:"swiper-lazy-preloader",lazyPreloadPrevNext:0,runCallbacksOnInit:!0,_emitClasses:!1};function f_(t,e){return function(i={}){const n=Object.keys(i)[0],s=i[n];if(typeof s!="object"||s===null){Ht(e,i);return}if(["navigation","pagination","scrollbar"].indexOf(n)>=0&&t[n]===!0&&(t[n]={auto:!0}),!(n in t&&"enabled"in s)){Ht(e,i);return}t[n]===!0&&(t[n]={enabled:!0}),typeof t[n]=="object"&&!("enabled"in t[n])&&(t[n].enabled=!0),t[n]||(t[n]={enabled:!1}),Ht(e,i)}}const Ma={eventsEmitter:og,update:vg,translate:Tg,transition:Mg,slide:zg,loop:Ng,grabCursor:Hg,events:e_,breakpoints:i_,checkOverflow:u_,classes:o_},Oa={};class Lt{constructor(...e){let r,i;e.length===1&&e[0].constructor&&Object.prototype.toString.call(e[0]).slice(8,-1)==="Object"?i=e[0]:[r,i]=e,i||(i={}),i=Ht({},i),r&&!i.el&&(i.el=r);const n=Mr();if(i.el&&typeof i.el=="string"&&n.querySelectorAll(i.el).length>1){const l=[];return n.querySelectorAll(i.el).forEach(u=>{const f=Ht({},i,{el:u});l.push(new Lt(f))}),l}const s=this;s.__swiper__=!0,s.support=ic(),s.device=rg({userAgent:i.userAgent}),s.browser=ng(),s.eventsListeners={},s.eventsAnyListeners=[],s.modules=[...s.__modules__],i.modules&&Array.isArray(i.modules)&&s.modules.push(...i.modules);const a={};s.modules.forEach(l=>{l({params:i,swiper:s,extendParams:f_(i,a),on:s.on.bind(s),once:s.once.bind(s),off:s.off.bind(s),emit:s.emit.bind(s)})});const o=Ht({},Zl,a);return s.params=Ht({},o,Oa,i),s.originalParams=Ht({},s.params),s.passedParams=Ht({},i),s.params&&s.params.on&&Object.keys(s.params.on).forEach(l=>{s.on(l,s.params.on[l])}),s.params&&s.params.onAny&&s.onAny(s.params.onAny),Object.assign(s,{enabled:s.params.enabled,el:r,classNames:[],slides:[],slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal(){return s.params.direction==="horizontal"},isVertical(){return s.params.direction==="vertical"},activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,cssOverflowAdjustment(){return Math.trunc(this.translate/2**23)*2**23},allowSlideNext:s.params.allowSlideNext,allowSlidePrev:s.params.allowSlidePrev,touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:s.params.focusableElements,lastClickTime:0,clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,startMoving:void 0,evCache:[]},allowClick:!0,allowTouchMove:s.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),s.emit("_swiper"),s.params.init&&s.init(),s}getSlideIndex(e){const{slidesEl:r,params:i}=this,n=Cr(r,`.${i.slideClass}, swiper-slide`),s=Xs(n[0]);return Xs(e)-s}getSlideIndexByData(e){return this.getSlideIndex(this.slides.filter(r=>r.getAttribute("data-swiper-slide-index")*1===e)[0])}recalcSlides(){const e=this,{slidesEl:r,params:i}=e;e.slides=Cr(r,`.${i.slideClass}, swiper-slide`)}enable(){const e=this;e.enabled||(e.enabled=!0,e.params.grabCursor&&e.setGrabCursor(),e.emit("enable"))}disable(){const e=this;e.enabled&&(e.enabled=!1,e.params.grabCursor&&e.unsetGrabCursor(),e.emit("disable"))}setProgress(e,r){const i=this;e=Math.min(Math.max(e,0),1);const n=i.minTranslate(),a=(i.maxTranslate()-n)*e+n;i.translateTo(a,typeof r>"u"?0:r),i.updateActiveIndex(),i.updateSlidesClasses()}emitContainerClasses(){const e=this;if(!e.params._emitClasses||!e.el)return;const r=e.el.className.split(" ").filter(i=>i.indexOf("swiper")===0||i.indexOf(e.params.containerModifierClass)===0);e.emit("_containerClasses",r.join(" "))}getSlideClasses(e){const r=this;return r.destroyed?"":e.className.split(" ").filter(i=>i.indexOf("swiper-slide")===0||i.indexOf(r.params.slideClass)===0).join(" ")}emitSlidesClasses(){const e=this;if(!e.params._emitClasses||!e.el)return;const r=[];e.slides.forEach(i=>{const n=e.getSlideClasses(i);r.push({slideEl:i,classNames:n}),e.emit("_slideClass",i,n)}),e.emit("_slideClasses",r)}slidesPerViewDynamic(e="current",r=!1){const i=this,{params:n,slides:s,slidesGrid:a,slidesSizesGrid:o,size:l,activeIndex:u}=i;let f=1;if(n.centeredSlides){let c=s[u]?s[u].swiperSlideSize:0,p;for(let d=u+1;d<s.length;d+=1)s[d]&&!p&&(c+=s[d].swiperSlideSize,f+=1,c>l&&(p=!0));for(let d=u-1;d>=0;d-=1)s[d]&&!p&&(c+=s[d].swiperSlideSize,f+=1,c>l&&(p=!0))}else if(e==="current")for(let c=u+1;c<s.length;c+=1)(r?a[c]+o[c]-a[u]<l:a[c]-a[u]<l)&&(f+=1);else for(let c=u-1;c>=0;c-=1)a[u]-a[c]<l&&(f+=1);return f}update(){const e=this;if(!e||e.destroyed)return;const{snapGrid:r,params:i}=e;i.breakpoints&&e.setBreakpoint(),[...e.el.querySelectorAll('[loading="lazy"]')].forEach(a=>{a.complete&&Ds(e,a)}),e.updateSize(),e.updateSlides(),e.updateProgress(),e.updateSlidesClasses();function n(){const a=e.rtlTranslate?e.translate*-1:e.translate,o=Math.min(Math.max(a,e.maxTranslate()),e.minTranslate());e.setTranslate(o),e.updateActiveIndex(),e.updateSlidesClasses()}let s;if(i.freeMode&&i.freeMode.enabled&&!i.cssMode)n(),i.autoHeight&&e.updateAutoHeight();else{if((i.slidesPerView==="auto"||i.slidesPerView>1)&&e.isEnd&&!i.centeredSlides){const a=e.virtual&&i.virtual.enabled?e.virtual.slides:e.slides;s=e.slideTo(a.length-1,0,!1,!0)}else s=e.slideTo(e.activeIndex,0,!1,!0);s||n()}i.watchOverflow&&r!==e.snapGrid&&e.checkOverflow(),e.emit("update")}changeDirection(e,r=!0){const i=this,n=i.params.direction;return e||(e=n==="horizontal"?"vertical":"horizontal"),e===n||e!=="horizontal"&&e!=="vertical"||(i.el.classList.remove(`${i.params.containerModifierClass}${n}`),i.el.classList.add(`${i.params.containerModifierClass}${e}`),i.emitContainerClasses(),i.params.direction=e,i.slides.forEach(s=>{e==="vertical"?s.style.width="":s.style.height=""}),i.emit("changeDirection"),r&&i.update()),i}changeLanguageDirection(e){const r=this;r.rtl&&e==="rtl"||!r.rtl&&e==="ltr"||(r.rtl=e==="rtl",r.rtlTranslate=r.params.direction==="horizontal"&&r.rtl,r.rtl?(r.el.classList.add(`${r.params.containerModifierClass}rtl`),r.el.dir="rtl"):(r.el.classList.remove(`${r.params.containerModifierClass}rtl`),r.el.dir="ltr"),r.update())}mount(e){const r=this;if(r.mounted)return!0;let i=e||r.params.el;if(typeof i=="string"&&(i=document.querySelector(i)),!i)return!1;i.swiper=r,i.shadowEl&&(r.isElement=!0);const n=()=>`.${(r.params.wrapperClass||"").trim().split(" ").join(".")}`;let a=(()=>i&&i.shadowRoot&&i.shadowRoot.querySelector?i.shadowRoot.querySelector(n()):Cr(i,n())[0])();return!a&&r.params.createElements&&(a=tc("div",r.params.wrapperClass),i.append(a),Cr(i,`.${r.params.slideClass}`).forEach(o=>{a.append(o)})),Object.assign(r,{el:i,wrapperEl:a,slidesEl:r.isElement?i:a,mounted:!0,rtl:i.dir.toLowerCase()==="rtl"||qr(i,"direction")==="rtl",rtlTranslate:r.params.direction==="horizontal"&&(i.dir.toLowerCase()==="rtl"||qr(i,"direction")==="rtl"),wrongRTL:qr(a,"display")==="-webkit-box"}),!0}init(e){const r=this;return r.initialized||r.mount(e)===!1||(r.emit("beforeInit"),r.params.breakpoints&&r.setBreakpoint(),r.addClasses(),r.updateSize(),r.updateSlides(),r.params.watchOverflow&&r.checkOverflow(),r.params.grabCursor&&r.enabled&&r.setGrabCursor(),r.params.loop&&r.virtual&&r.params.virtual.enabled?r.slideTo(r.params.initialSlide+r.virtual.slidesBefore,0,r.params.runCallbacksOnInit,!1,!0):r.slideTo(r.params.initialSlide,0,r.params.runCallbacksOnInit,!1,!0),r.params.loop&&r.loopCreate(),r.attachEvents(),[...r.el.querySelectorAll('[loading="lazy"]')].forEach(n=>{n.complete?Ds(r,n):n.addEventListener("load",s=>{Ds(r,s.target)})}),fo(r),r.initialized=!0,fo(r),r.emit("init"),r.emit("afterInit")),r}destroy(e=!0,r=!0){const i=this,{params:n,el:s,wrapperEl:a,slides:o}=i;return typeof i.params>"u"||i.destroyed||(i.emit("beforeDestroy"),i.initialized=!1,i.detachEvents(),n.loop&&i.loopDestroy(),r&&(i.removeClasses(),s.removeAttribute("style"),a.removeAttribute("style"),o&&o.length&&o.forEach(l=>{l.classList.remove(n.slideVisibleClass,n.slideActiveClass,n.slideNextClass,n.slidePrevClass),l.removeAttribute("style"),l.removeAttribute("data-swiper-slide-index")})),i.emit("destroy"),Object.keys(i.eventsListeners).forEach(l=>{i.off(l)}),e!==!1&&(i.el.swiper=null,Xh(i)),i.destroyed=!0),null}static extendDefaults(e){Ht(Oa,e)}static get extendedDefaults(){return Oa}static get defaults(){return Zl}static installModule(e){Lt.prototype.__modules__||(Lt.prototype.__modules__=[]);const r=Lt.prototype.__modules__;typeof e=="function"&&r.indexOf(e)<0&&r.push(e)}static use(e){return Array.isArray(e)?(e.forEach(r=>Lt.installModule(r)),Lt):(Lt.installModule(e),Lt)}}Object.keys(Ma).forEach(t=>{Object.keys(Ma[t]).forEach(e=>{Lt.prototype[e]=Ma[t][e]})});Lt.use([sg,ag]);function c_(t,e,r,i){return t.params.createElements&&Object.keys(i).forEach(n=>{if(!r[n]&&r.auto===!0){let s=Cr(t.el,`.${i[n]}`)[0];s||(s=tc("div",i[n]),s.className=i[n],t.el.append(s)),r[n]=s,e[n]=s}}),r}function xn(t=""){return`.${t.trim().replace(/([\.:!+\/])/g,"\\$1").replace(/ /g,".")}`}function ac({swiper:t,extendParams:e,on:r,emit:i}){const n="swiper-pagination";e({pagination:{el:null,bulletElement:"span",clickable:!1,hideOnClick:!1,renderBullet:null,renderProgressbar:null,renderFraction:null,renderCustom:null,progressbarOpposite:!1,type:"bullets",dynamicBullets:!1,dynamicMainBullets:1,formatFractionCurrent:g=>g,formatFractionTotal:g=>g,bulletClass:`${n}-bullet`,bulletActiveClass:`${n}-bullet-active`,modifierClass:`${n}-`,currentClass:`${n}-current`,totalClass:`${n}-total`,hiddenClass:`${n}-hidden`,progressbarFillClass:`${n}-progressbar-fill`,progressbarOppositeClass:`${n}-progressbar-opposite`,clickableClass:`${n}-clickable`,lockClass:`${n}-lock`,horizontalClass:`${n}-horizontal`,verticalClass:`${n}-vertical`,paginationDisabledClass:`${n}-disabled`}}),t.pagination={el:null,bullets:[]};let s,a=0;const o=g=>(Array.isArray(g)||(g=[g].filter(v=>!!v)),g);function l(){return!t.params.pagination.el||!t.pagination.el||Array.isArray(t.pagination.el)&&t.pagination.el.length===0}function u(g,v){const{bulletActiveClass:x}=t.params.pagination;g&&(g=g[`${v==="prev"?"previous":"next"}ElementSibling`],g&&(g.classList.add(`${x}-${v}`),g=g[`${v==="prev"?"previous":"next"}ElementSibling`],g&&g.classList.add(`${x}-${v}-${v}`)))}function f(g){const v=g.target.closest(xn(t.params.pagination.bulletClass));if(!v)return;g.preventDefault();const x=Xs(v)*t.params.slidesPerGroup;if(t.params.loop){if(t.realIndex===x)return;const y=t.getSlideIndexByData(x),b=t.getSlideIndexByData(t.realIndex);y>t.slides.length-t.loopedSlides&&t.loopFix({direction:y>b?"next":"prev",activeSlideIndex:y,slideTo:!1}),t.slideToLoop(x)}else t.slideTo(x)}function c(){const g=t.rtl,v=t.params.pagination;if(l())return;let x=t.pagination.el;x=o(x);let y,b;const w=t.virtual&&t.params.virtual.enabled?t.virtual.slides.length:t.slides.length,C=t.params.loop?Math.ceil(w/t.params.slidesPerGroup):t.snapGrid.length;if(t.params.loop?(b=t.previousRealIndex||0,y=t.params.slidesPerGroup>1?Math.floor(t.realIndex/t.params.slidesPerGroup):t.realIndex):typeof t.snapIndex<"u"?(y=t.snapIndex,b=t.previousSnapIndex):(b=t.previousIndex||0,y=t.activeIndex||0),v.type==="bullets"&&t.pagination.bullets&&t.pagination.bullets.length>0){const O=t.pagination.bullets;let M,A,k;if(v.dynamicBullets&&(s=uo(O[0],t.isHorizontal()?"width":"height",!0),x.forEach(E=>{E.style[t.isHorizontal()?"width":"height"]=`${s*(v.dynamicMainBullets+4)}px`}),v.dynamicMainBullets>1&&b!==void 0&&(a+=y-(b||0),a>v.dynamicMainBullets-1?a=v.dynamicMainBullets-1:a<0&&(a=0)),M=Math.max(y-a,0),A=M+(Math.min(O.length,v.dynamicMainBullets)-1),k=(A+M)/2),O.forEach(E=>{const L=[...["","-next","-next-next","-prev","-prev-prev","-main"].map(R=>`${v.bulletActiveClass}${R}`)].map(R=>typeof R=="string"&&R.includes(" ")?R.split(" "):R).flat();E.classList.remove(...L)}),x.length>1)O.forEach(E=>{const L=Xs(E);L===y?E.classList.add(...v.bulletActiveClass.split(" ")):t.isElement&&E.setAttribute("part","bullet"),v.dynamicBullets&&(L>=M&&L<=A&&E.classList.add(...`${v.bulletActiveClass}-main`.split(" ")),L===M&&u(E,"prev"),L===A&&u(E,"next"))});else{const E=O[y];if(E&&E.classList.add(...v.bulletActiveClass.split(" ")),t.isElement&&O.forEach((L,R)=>{L.setAttribute("part",R===y?"bullet-active":"bullet")}),v.dynamicBullets){const L=O[M],R=O[A];for(let H=M;H<=A;H+=1)O[H]&&O[H].classList.add(...`${v.bulletActiveClass}-main`.split(" "));u(L,"prev"),u(R,"next")}}if(v.dynamicBullets){const E=Math.min(O.length,v.dynamicMainBullets+4),L=(s*E-s)/2-k*s,R=g?"right":"left";O.forEach(H=>{H.style[t.isHorizontal()?R:"top"]=`${L}px`})}}x.forEach((O,M)=>{if(v.type==="fraction"&&(O.querySelectorAll(xn(v.currentClass)).forEach(A=>{A.textContent=v.formatFractionCurrent(y+1)}),O.querySelectorAll(xn(v.totalClass)).forEach(A=>{A.textContent=v.formatFractionTotal(C)})),v.type==="progressbar"){let A;v.progressbarOpposite?A=t.isHorizontal()?"vertical":"horizontal":A=t.isHorizontal()?"horizontal":"vertical";const k=(y+1)/C;let E=1,L=1;A==="horizontal"?E=k:L=k,O.querySelectorAll(xn(v.progressbarFillClass)).forEach(R=>{R.style.transform=`translate3d(0,0,0) scaleX(${E}) scaleY(${L})`,R.style.transitionDuration=`${t.params.speed}ms`})}v.type==="custom"&&v.renderCustom?(O.innerHTML=v.renderCustom(t,y+1,C),M===0&&i("paginationRender",O)):(M===0&&i("paginationRender",O),i("paginationUpdate",O)),t.params.watchOverflow&&t.enabled&&O.classList[t.isLocked?"add":"remove"](v.lockClass)})}function p(){const g=t.params.pagination;if(l())return;const v=t.virtual&&t.params.virtual.enabled?t.virtual.slides.length:t.slides.length;let x=t.pagination.el;x=o(x);let y="";if(g.type==="bullets"){let b=t.params.loop?Math.ceil(v/t.params.slidesPerGroup):t.snapGrid.length;t.params.freeMode&&t.params.freeMode.enabled&&b>v&&(b=v);for(let w=0;w<b;w+=1)g.renderBullet?y+=g.renderBullet.call(t,w,g.bulletClass):y+=`<${g.bulletElement} ${t.isElement?'part="bullet"':""} class="${g.bulletClass}"></${g.bulletElement}>`}g.type==="fraction"&&(g.renderFraction?y=g.renderFraction.call(t,g.currentClass,g.totalClass):y=`<span class="${g.currentClass}"></span> / <span class="${g.totalClass}"></span>`),g.type==="progressbar"&&(g.renderProgressbar?y=g.renderProgressbar.call(t,g.progressbarFillClass):y=`<span class="${g.progressbarFillClass}"></span>`),t.pagination.bullets=[],x.forEach(b=>{g.type!=="custom"&&(b.innerHTML=y||""),g.type==="bullets"&&t.pagination.bullets.push(...b.querySelectorAll(xn(g.bulletClass)))}),g.type!=="custom"&&i("paginationRender",x[0])}function d(){t.params.pagination=c_(t,t.originalParams.pagination,t.params.pagination,{el:"swiper-pagination"});const g=t.params.pagination;if(!g.el)return;let v;typeof g.el=="string"&&t.isElement&&(v=t.el.shadowRoot.querySelector(g.el)),!v&&typeof g.el=="string"&&(v=[...document.querySelectorAll(g.el)]),v||(v=g.el),!(!v||v.length===0)&&(t.params.uniqueNavElements&&typeof g.el=="string"&&Array.isArray(v)&&v.length>1&&(v=[...t.el.querySelectorAll(g.el)],v.length>1&&(v=v.filter(x=>rc(x,".swiper")[0]===t.el)[0])),Array.isArray(v)&&v.length===1&&(v=v[0]),Object.assign(t.pagination,{el:v}),v=o(v),v.forEach(x=>{g.type==="bullets"&&g.clickable&&x.classList.add(g.clickableClass),x.classList.add(g.modifierClass+g.type),x.classList.add(t.isHorizontal()?g.horizontalClass:g.verticalClass),g.type==="bullets"&&g.dynamicBullets&&(x.classList.add(`${g.modifierClass}${g.type}-dynamic`),a=0,g.dynamicMainBullets<1&&(g.dynamicMainBullets=1)),g.type==="progressbar"&&g.progressbarOpposite&&x.classList.add(g.progressbarOppositeClass),g.clickable&&x.addEventListener("click",f),t.enabled||x.classList.add(g.lockClass)}))}function _(){const g=t.params.pagination;if(l())return;let v=t.pagination.el;v&&(v=o(v),v.forEach(x=>{x.classList.remove(g.hiddenClass),x.classList.remove(g.modifierClass+g.type),x.classList.remove(t.isHorizontal()?g.horizontalClass:g.verticalClass),g.clickable&&x.removeEventListener("click",f)})),t.pagination.bullets&&t.pagination.bullets.forEach(x=>x.classList.remove(...g.bulletActiveClass.split(" ")))}r("changeDirection",()=>{if(!t.pagination||!t.pagination.el)return;const g=t.params.pagination;let{el:v}=t.pagination;v=o(v),v.forEach(x=>{x.classList.remove(g.horizontalClass,g.verticalClass),x.classList.add(t.isHorizontal()?g.horizontalClass:g.verticalClass)})}),r("init",()=>{t.params.pagination.enabled===!1?m():(d(),p(),c())}),r("activeIndexChange",()=>{typeof t.snapIndex>"u"&&c()}),r("snapIndexChange",()=>{c()}),r("snapGridLengthChange",()=>{p(),c()}),r("destroy",()=>{_()}),r("enable disable",()=>{let{el:g}=t.pagination;g&&(g=o(g),g.forEach(v=>v.classList[t.enabled?"remove":"add"](t.params.pagination.lockClass)))}),r("lock unlock",()=>{c()}),r("click",(g,v)=>{const x=v.target;let{el:y}=t.pagination;if(Array.isArray(y)||(y=[y].filter(b=>!!b)),t.params.pagination.el&&t.params.pagination.hideOnClick&&y&&y.length>0&&!x.classList.contains(t.params.pagination.bulletClass)){if(t.navigation&&(t.navigation.nextEl&&x===t.navigation.nextEl||t.navigation.prevEl&&x===t.navigation.prevEl))return;const b=y[0].classList.contains(t.params.pagination.hiddenClass);i(b===!0?"paginationShow":"paginationHide"),y.forEach(w=>w.classList.toggle(t.params.pagination.hiddenClass))}});const h=()=>{t.el.classList.remove(t.params.pagination.paginationDisabledClass);let{el:g}=t.pagination;g&&(g=o(g),g.forEach(v=>v.classList.remove(t.params.pagination.paginationDisabledClass))),d(),p(),c()},m=()=>{t.el.classList.add(t.params.pagination.paginationDisabledClass);let{el:g}=t.pagination;g&&(g=o(g),g.forEach(v=>v.classList.add(t.params.pagination.paginationDisabledClass))),_()};Object.assign(t.pagination,{enable:h,disable:m,render:p,update:c,init:d,destroy:_})}function oc({swiper:t,extendParams:e,on:r,emit:i,params:n}){t.autoplay={running:!1,paused:!1,timeLeft:0},e({autoplay:{enabled:!1,delay:3e3,waitForTransition:!0,disableOnInteraction:!0,stopOnLastSlide:!1,reverseDirection:!1,pauseOnMouseEnter:!1}});let s,a,o=n&&n.autoplay?n.autoplay.delay:3e3,l=n&&n.autoplay?n.autoplay.delay:3e3,u,f=new Date().getTime,c,p,d,_,h,m;function g(F){!t||t.destroyed||!t.wrapperEl||F.target===t.wrapperEl&&(t.wrapperEl.removeEventListener("transitionend",g),O())}const v=()=>{if(t.destroyed||!t.autoplay.running)return;t.autoplay.paused?c=!0:c&&(l=u,c=!1);const F=t.autoplay.paused?u:f+l-new Date().getTime();t.autoplay.timeLeft=F,i("autoplayTimeLeft",F,F/o),a=requestAnimationFrame(()=>{v()})},x=()=>{let F;return t.virtual&&t.params.virtual.enabled?F=t.slides.filter(j=>j.classList.contains("swiper-slide-active"))[0]:F=t.slides[t.activeIndex],F?parseInt(F.getAttribute("data-swiper-autoplay"),10):void 0},y=F=>{if(t.destroyed||!t.autoplay.running)return;cancelAnimationFrame(a),v();let B=typeof F>"u"?t.params.autoplay.delay:F;o=t.params.autoplay.delay,l=t.params.autoplay.delay;const j=x();!Number.isNaN(j)&&j>0&&typeof F>"u"&&(B=j,o=j,l=j),u=B;const Z=t.params.speed,S=()=>{!t||t.destroyed||(t.params.autoplay.reverseDirection?!t.isBeginning||t.params.loop||t.params.rewind?(t.slidePrev(Z,!0,!0),i("autoplay")):t.params.autoplay.stopOnLastSlide||(t.slideTo(t.slides.length-1,Z,!0,!0),i("autoplay")):!t.isEnd||t.params.loop||t.params.rewind?(t.slideNext(Z,!0,!0),i("autoplay")):t.params.autoplay.stopOnLastSlide||(t.slideTo(0,Z,!0,!0),i("autoplay")),t.params.cssMode&&(f=new Date().getTime(),requestAnimationFrame(()=>{y()})))};return B>0?(clearTimeout(s),s=setTimeout(()=>{S()},B)):requestAnimationFrame(()=>{S()}),B},b=()=>{t.autoplay.running=!0,y(),i("autoplayStart")},w=()=>{t.autoplay.running=!1,clearTimeout(s),cancelAnimationFrame(a),i("autoplayStop")},C=(F,B)=>{if(t.destroyed||!t.autoplay.running)return;clearTimeout(s),F||(m=!0);const j=()=>{i("autoplayPause"),t.params.autoplay.waitForTransition?t.wrapperEl.addEventListener("transitionend",g):O()};if(t.autoplay.paused=!0,B){h&&(u=t.params.autoplay.delay),h=!1,j();return}u=(u||t.params.autoplay.delay)-(new Date().getTime()-f),!(t.isEnd&&u<0&&!t.params.loop)&&(u<0&&(u=0),j())},O=()=>{t.isEnd&&u<0&&!t.params.loop||t.destroyed||!t.autoplay.running||(f=new Date().getTime(),m?(m=!1,y(u)):y(),t.autoplay.paused=!1,i("autoplayResume"))},M=()=>{if(t.destroyed||!t.autoplay.running)return;const F=Mr();F.visibilityState==="hidden"&&(m=!0,C(!0)),F.visibilityState==="visible"&&O()},A=F=>{F.pointerType==="mouse"&&(m=!0,C(!0))},k=F=>{F.pointerType==="mouse"&&t.autoplay.paused&&O()},E=()=>{t.params.autoplay.pauseOnMouseEnter&&(t.el.addEventListener("pointerenter",A),t.el.addEventListener("pointerleave",k))},L=()=>{t.el.removeEventListener("pointerenter",A),t.el.removeEventListener("pointerleave",k)},R=()=>{Mr().addEventListener("visibilitychange",M)},H=()=>{Mr().removeEventListener("visibilitychange",M)};r("init",()=>{t.params.autoplay.enabled&&(E(),R(),f=new Date().getTime(),b())}),r("destroy",()=>{L(),H(),t.autoplay.running&&w()}),r("beforeTransitionStart",(F,B,j)=>{t.destroyed||!t.autoplay.running||(j||!t.params.autoplay.disableOnInteraction?C(!0,!0):w())}),r("sliderFirstMove",()=>{if(!(t.destroyed||!t.autoplay.running)){if(t.params.autoplay.disableOnInteraction){w();return}p=!0,d=!1,m=!1,_=setTimeout(()=>{m=!0,d=!0,C(!0)},200)}}),r("touchEnd",()=>{if(!(t.destroyed||!t.autoplay.running||!p)){if(clearTimeout(_),clearTimeout(s),t.params.autoplay.disableOnInteraction){d=!1,p=!1;return}d&&t.params.cssMode&&O(),d=!1,p=!1}}),r("slideChange",()=>{t.destroyed||!t.autoplay.running||(h=!0)}),Object.assign(t.autoplay,{start:b,stop:w,pause:C,resume:O})}const d_={init(){new Lt(".js-our-work-slider",{modules:[ac,oc],autoplay:!1,pagination:{el:".swiper-pagination",clickable:!0},slidesPerView:1.2,spaceBetween:24,speed:2e3,grabCursor:!0,autoplay:{delay:5e3},breakpoints:{1440:{slidesPerView:1.8},1920:{slidesPerView:2.3}}})}};function Dr(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function lc(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t,t.__proto__=e}/*!
 * GSAP 3.13.0
 * https://gsap.com
 *
 * @license Copyright 2008-2025, GreenSock. All rights reserved.
 * Subject to the terms at https://gsap.com/standard-license
 * @author: Jack Doyle, <EMAIL>
*/var Ut={autoSleep:120,force3D:"auto",nullTargetWarn:1,units:{lineHeight:""}},rn={duration:.5,overwrite:!1,delay:0},al,ft,Ce,nr=1e8,ye=1/nr,co=Math.PI*2,p_=co/4,h_=0,uc=Math.sqrt,g_=Math.cos,__=Math.sin,st=function(e){return typeof e=="string"},Re=function(e){return typeof e=="function"},Br=function(e){return typeof e=="number"},ol=function(e){return typeof e>"u"},Ar=function(e){return typeof e=="object"},Dt=function(e){return e!==!1},ll=function(){return typeof window<"u"},_s=function(e){return Re(e)||st(e)},fc=typeof ArrayBuffer=="function"&&ArrayBuffer.isView||function(){},yt=Array.isArray,po=/(?:-?\.?\d|\.)+/gi,cc=/[-+=.]*\d+[.e\-+]*\d*[e\-+]*\d*/g,ji=/[-+=.]*\d+[.e-]*\d*[a-z%]*/g,Aa=/[-+=.]*\d+\.?\d*(?:e-|e\+)?\d*/gi,dc=/[+-]=-?[.\d]+/,pc=/[^,'"\[\]\s]+/gi,m_=/^[+\-=e\s\d]*\d+[.\d]*([a-z]*|%)\s*$/i,Oe,br,ho,ul,Kt={},Us={},hc,gc=function(e){return(Us=nn(e,Kt))&&Ft},fl=function(e,r){return console.warn("Invalid property",e,"set to",r,"Missing plugin? gsap.registerPlugin()")},Xn=function(e,r){return!r&&console.warn(e)},_c=function(e,r){return e&&(Kt[e]=r)&&Us&&(Us[e]=r)||Kt},Un=function(){return 0},v_={suppressEvents:!0,isStart:!0,kill:!1},Is={suppressEvents:!0,kill:!1},y_={suppressEvents:!0},cl={},Qr=[],go={},mc,Yt={},ka={},Ql=30,Rs=[],dl="",pl=function(e){var r=e[0],i,n;if(Ar(r)||Re(r)||(e=[e]),!(i=(r._gsap||{}).harness)){for(n=Rs.length;n--&&!Rs[n].targetTest(r););i=Rs[n]}for(n=e.length;n--;)e[n]&&(e[n]._gsap||(e[n]._gsap=new Vc(e[n],i)))||e.splice(n,1);return e},Ci=function(e){return e._gsap||pl(sr(e))[0]._gsap},vc=function(e,r,i){return(i=e[r])&&Re(i)?e[r]():ol(i)&&e.getAttribute&&e.getAttribute(r)||i},It=function(e,r){return(e=e.split(",")).forEach(r)||e},$e=function(e){return Math.round(e*1e5)/1e5||0},He=function(e){return Math.round(e*1e7)/1e7||0},Xi=function(e,r){var i=r.charAt(0),n=parseFloat(r.substr(2));return e=parseFloat(e),i==="+"?e+n:i==="-"?e-n:i==="*"?e*n:e/n},x_=function(e,r){for(var i=r.length,n=0;e.indexOf(r[n])<0&&++n<i;);return n<i},Ks=function(){var e=Qr.length,r=Qr.slice(0),i,n;for(go={},Qr.length=0,i=0;i<e;i++)n=r[i],n&&n._lazy&&(n.render(n._lazy[0],n._lazy[1],!0)._lazy=0)},hl=function(e){return!!(e._initted||e._startAt||e.add)},yc=function(e,r,i,n){Qr.length&&!ft&&Ks(),e.render(r,i,n||!!(ft&&r<0&&hl(e))),Qr.length&&!ft&&Ks()},xc=function(e){var r=parseFloat(e);return(r||r===0)&&(e+"").match(pc).length<2?r:st(e)?e.trim():e},bc=function(e){return e},Jt=function(e,r){for(var i in r)i in e||(e[i]=r[i]);return e},b_=function(e){return function(r,i){for(var n in i)n in r||n==="duration"&&e||n==="ease"||(r[n]=i[n])}},nn=function(e,r){for(var i in r)e[i]=r[i];return e},eu=function t(e,r){for(var i in r)i!=="__proto__"&&i!=="constructor"&&i!=="prototype"&&(e[i]=Ar(r[i])?t(e[i]||(e[i]={}),r[i]):r[i]);return e},Js=function(e,r){var i={},n;for(n in e)n in r||(i[n]=e[n]);return i},Rn=function(e){var r=e.parent||Oe,i=e.keyframes?b_(yt(e.keyframes)):Jt;if(Dt(e.inherit))for(;r;)i(e,r.vars.defaults),r=r.parent||r._dp;return e},S_=function(e,r){for(var i=e.length,n=i===r.length;n&&i--&&e[i]===r[i];);return i<0},Sc=function(e,r,i,n,s){i===void 0&&(i="_first"),n===void 0&&(n="_last");var a=e[n],o;if(s)for(o=r[s];a&&a[s]>o;)a=a._prev;return a?(r._next=a._next,a._next=r):(r._next=e[i],e[i]=r),r._next?r._next._prev=r:e[n]=r,r._prev=a,r.parent=r._dp=e,r},ha=function(e,r,i,n){i===void 0&&(i="_first"),n===void 0&&(n="_last");var s=r._prev,a=r._next;s?s._next=a:e[i]===r&&(e[i]=a),a?a._prev=s:e[n]===r&&(e[n]=s),r._next=r._prev=r.parent=null},ni=function(e,r){e.parent&&(!r||e.parent.autoRemoveChildren)&&e.parent.remove&&e.parent.remove(e),e._act=0},Ei=function(e,r){if(e&&(!r||r._end>e._dur||r._start<0))for(var i=e;i;)i._dirty=1,i=i.parent;return e},w_=function(e){for(var r=e.parent;r&&r.parent;)r._dirty=1,r.totalDuration(),r=r.parent;return e},_o=function(e,r,i,n){return e._startAt&&(ft?e._startAt.revert(Is):e.vars.immediateRender&&!e.vars.autoRevert||e._startAt.render(r,!0,n))},T_=function t(e){return!e||e._ts&&t(e.parent)},tu=function(e){return e._repeat?sn(e._tTime,e=e.duration()+e._rDelay)*e:0},sn=function(e,r){var i=Math.floor(e=He(e/r));return e&&i===e?i-1:i},Zs=function(e,r){return(e-r._start)*r._ts+(r._ts>=0?0:r._dirty?r.totalDuration():r._tDur)},ga=function(e){return e._end=He(e._start+(e._tDur/Math.abs(e._ts||e._rts||ye)||0))},_a=function(e,r){var i=e._dp;return i&&i.smoothChildTiming&&e._ts&&(e._start=He(i._time-(e._ts>0?r/e._ts:((e._dirty?e.totalDuration():e._tDur)-r)/-e._ts)),ga(e),i._dirty||Ei(i,e)),e},wc=function(e,r){var i;if((r._time||!r._dur&&r._initted||r._start<e._time&&(r._dur||!r.add))&&(i=Zs(e.rawTime(),r),(!r._dur||as(0,r.totalDuration(),i)-r._tTime>ye)&&r.render(i,!0)),Ei(e,r)._dp&&e._initted&&e._time>=e._dur&&e._ts){if(e._dur<e.duration())for(i=e;i._dp;)i.rawTime()>=0&&i.totalTime(i._tTime),i=i._dp;e._zTime=-ye}},Tr=function(e,r,i,n){return r.parent&&ni(r),r._start=He((Br(i)?i:i||e!==Oe?er(e,i,r):e._time)+r._delay),r._end=He(r._start+(r.totalDuration()/Math.abs(r.timeScale())||0)),Sc(e,r,"_first","_last",e._sort?"_start":0),mo(r)||(e._recent=r),n||wc(e,r),e._ts<0&&_a(e,e._tTime),e},Tc=function(e,r){return(Kt.ScrollTrigger||fl("scrollTrigger",r))&&Kt.ScrollTrigger.create(r,e)},Cc=function(e,r,i,n,s){if(_l(e,r,s),!e._initted)return 1;if(!i&&e._pt&&!ft&&(e._dur&&e.vars.lazy!==!1||!e._dur&&e.vars.lazy)&&mc!==Wt.frame)return Qr.push(e),e._lazy=[s,n],1},C_=function t(e){var r=e.parent;return r&&r._ts&&r._initted&&!r._lock&&(r.rawTime()<0||t(r))},mo=function(e){var r=e.data;return r==="isFromStart"||r==="isStart"},E_=function(e,r,i,n){var s=e.ratio,a=r<0||!r&&(!e._start&&C_(e)&&!(!e._initted&&mo(e))||(e._ts<0||e._dp._ts<0)&&!mo(e))?0:1,o=e._rDelay,l=0,u,f,c;if(o&&e._repeat&&(l=as(0,e._tDur,r),f=sn(l,o),e._yoyo&&f&1&&(a=1-a),f!==sn(e._tTime,o)&&(s=1-a,e.vars.repeatRefresh&&e._initted&&e.invalidate())),a!==s||ft||n||e._zTime===ye||!r&&e._zTime){if(!e._initted&&Cc(e,r,n,i,l))return;for(c=e._zTime,e._zTime=r||(i?ye:0),i||(i=r&&!c),e.ratio=a,e._from&&(a=1-a),e._time=0,e._tTime=l,u=e._pt;u;)u.r(a,u.d),u=u._next;r<0&&_o(e,r,i,!0),e._onUpdate&&!i&&Xt(e,"onUpdate"),l&&e._repeat&&!i&&e.parent&&Xt(e,"onRepeat"),(r>=e._tDur||r<0)&&e.ratio===a&&(a&&ni(e,1),!i&&!ft&&(Xt(e,a?"onComplete":"onReverseComplete",!0),e._prom&&e._prom()))}else e._zTime||(e._zTime=r)},P_=function(e,r,i){var n;if(i>r)for(n=e._first;n&&n._start<=i;){if(n.data==="isPause"&&n._start>r)return n;n=n._next}else for(n=e._last;n&&n._start>=i;){if(n.data==="isPause"&&n._start<r)return n;n=n._prev}},an=function(e,r,i,n){var s=e._repeat,a=He(r)||0,o=e._tTime/e._tDur;return o&&!n&&(e._time*=a/e._dur),e._dur=a,e._tDur=s?s<0?1e10:He(a*(s+1)+e._rDelay*s):a,o>0&&!n&&_a(e,e._tTime=e._tDur*o),e.parent&&ga(e),i||Ei(e.parent,e),e},ru=function(e){return e instanceof Pt?Ei(e):an(e,e._dur)},M_={_start:0,endTime:Un,totalDuration:Un},er=function t(e,r,i){var n=e.labels,s=e._recent||M_,a=e.duration()>=nr?s.endTime(!1):e._dur,o,l,u;return st(r)&&(isNaN(r)||r in n)?(l=r.charAt(0),u=r.substr(-1)==="%",o=r.indexOf("="),l==="<"||l===">"?(o>=0&&(r=r.replace(/=/,"")),(l==="<"?s._start:s.endTime(s._repeat>=0))+(parseFloat(r.substr(1))||0)*(u?(o<0?s:i).totalDuration()/100:1)):o<0?(r in n||(n[r]=a),n[r]):(l=parseFloat(r.charAt(o-1)+r.substr(o+1)),u&&i&&(l=l/100*(yt(i)?i[0]:i).totalDuration()),o>1?t(e,r.substr(0,o-1),i)+l:a+l)):r==null?a:+r},zn=function(e,r,i){var n=Br(r[1]),s=(n?2:1)+(e<2?0:1),a=r[s],o,l;if(n&&(a.duration=r[1]),a.parent=i,e){for(o=a,l=i;l&&!("immediateRender"in o);)o=l.vars.defaults||{},l=Dt(l.vars.inherit)&&l.parent;a.immediateRender=Dt(o.immediateRender),e<2?a.runBackwards=1:a.startAt=r[s-1]}return new Ge(r[0],a,r[s+1])},ui=function(e,r){return e||e===0?r(e):r},as=function(e,r,i){return i<e?e:i>r?r:i},mt=function(e,r){return!st(e)||!(r=m_.exec(e))?"":r[1]},O_=function(e,r,i){return ui(i,function(n){return as(e,r,n)})},vo=[].slice,Ec=function(e,r){return e&&Ar(e)&&"length"in e&&(!r&&!e.length||e.length-1 in e&&Ar(e[0]))&&!e.nodeType&&e!==br},A_=function(e,r,i){return i===void 0&&(i=[]),e.forEach(function(n){var s;return st(n)&&!r||Ec(n,1)?(s=i).push.apply(s,sr(n)):i.push(n)})||i},sr=function(e,r,i){return Ce&&!r&&Ce.selector?Ce.selector(e):st(e)&&!i&&(ho||!on())?vo.call((r||ul).querySelectorAll(e),0):yt(e)?A_(e,i):Ec(e)?vo.call(e,0):e?[e]:[]},yo=function(e){return e=sr(e)[0]||Xn("Invalid scope")||{},function(r){var i=e.current||e.nativeElement||e;return sr(r,i.querySelectorAll?i:i===e?Xn("Invalid scope")||ul.createElement("div"):e)}},Pc=function(e){return e.sort(function(){return .5-Math.random()})},Mc=function(e){if(Re(e))return e;var r=Ar(e)?e:{each:e},i=Pi(r.ease),n=r.from||0,s=parseFloat(r.base)||0,a={},o=n>0&&n<1,l=isNaN(n)||o,u=r.axis,f=n,c=n;return st(n)?f=c={center:.5,edges:.5,end:1}[n]||0:!o&&l&&(f=n[0],c=n[1]),function(p,d,_){var h=(_||r).length,m=a[h],g,v,x,y,b,w,C,O,M;if(!m){if(M=r.grid==="auto"?0:(r.grid||[1,nr])[1],!M){for(C=-nr;C<(C=_[M++].getBoundingClientRect().left)&&M<h;);M<h&&M--}for(m=a[h]=[],g=l?Math.min(M,h)*f-.5:n%M,v=M===nr?0:l?h*c/M-.5:n/M|0,C=0,O=nr,w=0;w<h;w++)x=w%M-g,y=v-(w/M|0),m[w]=b=u?Math.abs(u==="y"?y:x):uc(x*x+y*y),b>C&&(C=b),b<O&&(O=b);n==="random"&&Pc(m),m.max=C-O,m.min=O,m.v=h=(parseFloat(r.amount)||parseFloat(r.each)*(M>h?h-1:u?u==="y"?h/M:M:Math.max(M,h/M))||0)*(n==="edges"?-1:1),m.b=h<0?s-h:s,m.u=mt(r.amount||r.each)||0,i=i&&h<0?$c(i):i}return h=(m[p]-m.min)/m.max||0,He(m.b+(i?i(h):h)*m.v)+m.u}},xo=function(e){var r=Math.pow(10,((e+"").split(".")[1]||"").length);return function(i){var n=He(Math.round(parseFloat(i)/e)*e*r);return(n-n%1)/r+(Br(i)?0:mt(i))}},Oc=function(e,r){var i=yt(e),n,s;return!i&&Ar(e)&&(n=i=e.radius||nr,e.values?(e=sr(e.values),(s=!Br(e[0]))&&(n*=n)):e=xo(e.increment)),ui(r,i?Re(e)?function(a){return s=e(a),Math.abs(s-a)<=n?s:a}:function(a){for(var o=parseFloat(s?a.x:a),l=parseFloat(s?a.y:0),u=nr,f=0,c=e.length,p,d;c--;)s?(p=e[c].x-o,d=e[c].y-l,p=p*p+d*d):p=Math.abs(e[c]-o),p<u&&(u=p,f=c);return f=!n||u<=n?e[f]:a,s||f===a||Br(a)?f:f+mt(a)}:xo(e))},Ac=function(e,r,i,n){return ui(yt(e)?!r:i===!0?!!(i=0):!n,function(){return yt(e)?e[~~(Math.random()*e.length)]:(i=i||1e-5)&&(n=i<1?Math.pow(10,(i+"").length-2):1)&&Math.floor(Math.round((e-i/2+Math.random()*(r-e+i*.99))/i)*i*n)/n})},k_=function(){for(var e=arguments.length,r=new Array(e),i=0;i<e;i++)r[i]=arguments[i];return function(n){return r.reduce(function(s,a){return a(s)},n)}},L_=function(e,r){return function(i){return e(parseFloat(i))+(r||mt(i))}},D_=function(e,r,i){return Lc(e,r,0,1,i)},kc=function(e,r,i){return ui(i,function(n){return e[~~r(n)]})},I_=function t(e,r,i){var n=r-e;return yt(e)?kc(e,t(0,e.length),r):ui(i,function(s){return(n+(s-e)%n)%n+e})},R_=function t(e,r,i){var n=r-e,s=n*2;return yt(e)?kc(e,t(0,e.length-1),r):ui(i,function(a){return a=(s+(a-e)%s)%s||0,e+(a>n?s-a:a)})},Kn=function(e){for(var r=0,i="",n,s,a,o;~(n=e.indexOf("random(",r));)a=e.indexOf(")",n),o=e.charAt(n+7)==="[",s=e.substr(n+7,a-n-7).match(o?pc:po),i+=e.substr(r,n-r)+Ac(o?s:+s[0],o?0:+s[1],+s[2]||1e-5),r=a+1;return i+e.substr(r,e.length-r)},Lc=function(e,r,i,n,s){var a=r-e,o=n-i;return ui(s,function(l){return i+((l-e)/a*o||0)})},z_=function t(e,r,i,n){var s=isNaN(e+r)?0:function(d){return(1-d)*e+d*r};if(!s){var a=st(e),o={},l,u,f,c,p;if(i===!0&&(n=1)&&(i=null),a)e={p:e},r={p:r};else if(yt(e)&&!yt(r)){for(f=[],c=e.length,p=c-2,u=1;u<c;u++)f.push(t(e[u-1],e[u]));c--,s=function(_){_*=c;var h=Math.min(p,~~_);return f[h](_-h)},i=r}else n||(e=nn(yt(e)?[]:{},e));if(!f){for(l in r)gl.call(o,e,l,"get",r[l]);s=function(_){return yl(_,o)||(a?e.p:e)}}}return ui(i,s)},iu=function(e,r,i){var n=e.labels,s=nr,a,o,l;for(a in n)o=n[a]-r,o<0==!!i&&o&&s>(o=Math.abs(o))&&(l=a,s=o);return l},Xt=function(e,r,i){var n=e.vars,s=n[r],a=Ce,o=e._ctx,l,u,f;if(s)return l=n[r+"Params"],u=n.callbackScope||e,i&&Qr.length&&Ks(),o&&(Ce=o),f=l?s.apply(u,l):s.call(u),Ce=a,f},Cn=function(e){return ni(e),e.scrollTrigger&&e.scrollTrigger.kill(!!ft),e.progress()<1&&Xt(e,"onInterrupt"),e},Wi,Dc=[],Ic=function(e){if(e)if(e=!e.name&&e.default||e,ll()||e.headless){var r=e.name,i=Re(e),n=r&&!i&&e.init?function(){this._props=[]}:e,s={init:Un,render:yl,add:gl,kill:Z_,modifier:J_,rawVars:0},a={targetTest:0,get:0,getSetter:vl,aliases:{},register:0};if(on(),e!==n){if(Yt[r])return;Jt(n,Jt(Js(e,s),a)),nn(n.prototype,nn(s,Js(e,a))),Yt[n.prop=r]=n,e.targetTest&&(Rs.push(n),cl[r]=1),r=(r==="css"?"CSS":r.charAt(0).toUpperCase()+r.substr(1))+"Plugin"}_c(r,n),e.register&&e.register(Ft,n,Rt)}else Dc.push(e)},ve=255,En={aqua:[0,ve,ve],lime:[0,ve,0],silver:[192,192,192],black:[0,0,0],maroon:[128,0,0],teal:[0,128,128],blue:[0,0,ve],navy:[0,0,128],white:[ve,ve,ve],olive:[128,128,0],yellow:[ve,ve,0],orange:[ve,165,0],gray:[128,128,128],purple:[128,0,128],green:[0,128,0],red:[ve,0,0],pink:[ve,192,203],cyan:[0,ve,ve],transparent:[ve,ve,ve,0]},La=function(e,r,i){return e+=e<0?1:e>1?-1:0,(e*6<1?r+(i-r)*e*6:e<.5?i:e*3<2?r+(i-r)*(2/3-e)*6:r)*ve+.5|0},Rc=function(e,r,i){var n=e?Br(e)?[e>>16,e>>8&ve,e&ve]:0:En.black,s,a,o,l,u,f,c,p,d,_;if(!n){if(e.substr(-1)===","&&(e=e.substr(0,e.length-1)),En[e])n=En[e];else if(e.charAt(0)==="#"){if(e.length<6&&(s=e.charAt(1),a=e.charAt(2),o=e.charAt(3),e="#"+s+s+a+a+o+o+(e.length===5?e.charAt(4)+e.charAt(4):"")),e.length===9)return n=parseInt(e.substr(1,6),16),[n>>16,n>>8&ve,n&ve,parseInt(e.substr(7),16)/255];e=parseInt(e.substr(1),16),n=[e>>16,e>>8&ve,e&ve]}else if(e.substr(0,3)==="hsl"){if(n=_=e.match(po),!r)l=+n[0]%360/360,u=+n[1]/100,f=+n[2]/100,a=f<=.5?f*(u+1):f+u-f*u,s=f*2-a,n.length>3&&(n[3]*=1),n[0]=La(l+1/3,s,a),n[1]=La(l,s,a),n[2]=La(l-1/3,s,a);else if(~e.indexOf("="))return n=e.match(cc),i&&n.length<4&&(n[3]=1),n}else n=e.match(po)||En.transparent;n=n.map(Number)}return r&&!_&&(s=n[0]/ve,a=n[1]/ve,o=n[2]/ve,c=Math.max(s,a,o),p=Math.min(s,a,o),f=(c+p)/2,c===p?l=u=0:(d=c-p,u=f>.5?d/(2-c-p):d/(c+p),l=c===s?(a-o)/d+(a<o?6:0):c===a?(o-s)/d+2:(s-a)/d+4,l*=60),n[0]=~~(l+.5),n[1]=~~(u*100+.5),n[2]=~~(f*100+.5)),i&&n.length<4&&(n[3]=1),n},zc=function(e){var r=[],i=[],n=-1;return e.split(ei).forEach(function(s){var a=s.match(ji)||[];r.push.apply(r,a),i.push(n+=a.length+1)}),r.c=i,r},nu=function(e,r,i){var n="",s=(e+n).match(ei),a=r?"hsla(":"rgba(",o=0,l,u,f,c;if(!s)return e;if(s=s.map(function(p){return(p=Rc(p,r,1))&&a+(r?p[0]+","+p[1]+"%,"+p[2]+"%,"+p[3]:p.join(","))+")"}),i&&(f=zc(e),l=i.c,l.join(n)!==f.c.join(n)))for(u=e.replace(ei,"1").split(ji),c=u.length-1;o<c;o++)n+=u[o]+(~l.indexOf(o)?s.shift()||a+"0,0,0,0)":(f.length?f:s.length?s:i).shift());if(!u)for(u=e.split(ei),c=u.length-1;o<c;o++)n+=u[o]+s[o];return n+u[c]},ei=function(){var t="(?:\\b(?:(?:rgb|rgba|hsl|hsla)\\(.+?\\))|\\B#(?:[0-9a-f]{3,4}){1,2}\\b",e;for(e in En)t+="|"+e+"\\b";return new RegExp(t+")","gi")}(),F_=/hsl[a]?\(/,Fc=function(e){var r=e.join(" "),i;if(ei.lastIndex=0,ei.test(r))return i=F_.test(r),e[1]=nu(e[1],i),e[0]=nu(e[0],i,zc(e[1])),!0},Jn,Wt=function(){var t=Date.now,e=500,r=33,i=t(),n=i,s=1e3/240,a=s,o=[],l,u,f,c,p,d,_=function h(m){var g=t()-n,v=m===!0,x,y,b,w;if((g>e||g<0)&&(i+=g-r),n+=g,b=n-i,x=b-a,(x>0||v)&&(w=++c.frame,p=b-c.time*1e3,c.time=b=b/1e3,a+=x+(x>=s?4:s-x),y=1),v||(l=u(h)),y)for(d=0;d<o.length;d++)o[d](b,p,w,m)};return c={time:0,frame:0,tick:function(){_(!0)},deltaRatio:function(m){return p/(1e3/(m||60))},wake:function(){hc&&(!ho&&ll()&&(br=ho=window,ul=br.document||{},Kt.gsap=Ft,(br.gsapVersions||(br.gsapVersions=[])).push(Ft.version),gc(Us||br.GreenSockGlobals||!br.gsap&&br||{}),Dc.forEach(Ic)),f=typeof requestAnimationFrame<"u"&&requestAnimationFrame,l&&c.sleep(),u=f||function(m){return setTimeout(m,a-c.time*1e3+1|0)},Jn=1,_(2))},sleep:function(){(f?cancelAnimationFrame:clearTimeout)(l),Jn=0,u=Un},lagSmoothing:function(m,g){e=m||1/0,r=Math.min(g||33,e)},fps:function(m){s=1e3/(m||240),a=c.time*1e3+s},add:function(m,g,v){var x=g?function(y,b,w,C){m(y,b,w,C),c.remove(x)}:m;return c.remove(m),o[v?"unshift":"push"](x),on(),x},remove:function(m,g){~(g=o.indexOf(m))&&o.splice(g,1)&&d>=g&&d--},_listeners:o},c}(),on=function(){return!Jn&&Wt.wake()},ue={},$_=/^[\d.\-M][\d.\-,\s]/,B_=/["']/g,N_=function(e){for(var r={},i=e.substr(1,e.length-3).split(":"),n=i[0],s=1,a=i.length,o,l,u;s<a;s++)l=i[s],o=s!==a-1?l.lastIndexOf(","):l.length,u=l.substr(0,o),r[n]=isNaN(u)?u.replace(B_,"").trim():+u,n=l.substr(o+1).trim();return r},V_=function(e){var r=e.indexOf("(")+1,i=e.indexOf(")"),n=e.indexOf("(",r);return e.substring(r,~n&&n<i?e.indexOf(")",i+1):i)},G_=function(e){var r=(e+"").split("("),i=ue[r[0]];return i&&r.length>1&&i.config?i.config.apply(null,~e.indexOf("{")?[N_(r[1])]:V_(e).split(",").map(xc)):ue._CE&&$_.test(e)?ue._CE("",e):i},$c=function(e){return function(r){return 1-e(1-r)}},Bc=function t(e,r){for(var i=e._first,n;i;)i instanceof Pt?t(i,r):i.vars.yoyoEase&&(!i._yoyo||!i._repeat)&&i._yoyo!==r&&(i.timeline?t(i.timeline,r):(n=i._ease,i._ease=i._yEase,i._yEase=n,i._yoyo=r)),i=i._next},Pi=function(e,r){return e&&(Re(e)?e:ue[e]||G_(e))||r},Bi=function(e,r,i,n){i===void 0&&(i=function(l){return 1-r(1-l)}),n===void 0&&(n=function(l){return l<.5?r(l*2)/2:1-r((1-l)*2)/2});var s={easeIn:r,easeOut:i,easeInOut:n},a;return It(e,function(o){ue[o]=Kt[o]=s,ue[a=o.toLowerCase()]=i;for(var l in s)ue[a+(l==="easeIn"?".in":l==="easeOut"?".out":".inOut")]=ue[o+"."+l]=s[l]}),s},Nc=function(e){return function(r){return r<.5?(1-e(1-r*2))/2:.5+e((r-.5)*2)/2}},Da=function t(e,r,i){var n=r>=1?r:1,s=(i||(e?.3:.45))/(r<1?r:1),a=s/co*(Math.asin(1/n)||0),o=function(f){return f===1?1:n*Math.pow(2,-10*f)*__((f-a)*s)+1},l=e==="out"?o:e==="in"?function(u){return 1-o(1-u)}:Nc(o);return s=co/s,l.config=function(u,f){return t(e,u,f)},l},Ia=function t(e,r){r===void 0&&(r=1.70158);var i=function(a){return a?--a*a*((r+1)*a+r)+1:0},n=e==="out"?i:e==="in"?function(s){return 1-i(1-s)}:Nc(i);return n.config=function(s){return t(e,s)},n};It("Linear,Quad,Cubic,Quart,Quint,Strong",function(t,e){var r=e<5?e+1:e;Bi(t+",Power"+(r-1),e?function(i){return Math.pow(i,r)}:function(i){return i},function(i){return 1-Math.pow(1-i,r)},function(i){return i<.5?Math.pow(i*2,r)/2:1-Math.pow((1-i)*2,r)/2})});ue.Linear.easeNone=ue.none=ue.Linear.easeIn;Bi("Elastic",Da("in"),Da("out"),Da());(function(t,e){var r=1/e,i=2*r,n=2.5*r,s=function(o){return o<r?t*o*o:o<i?t*Math.pow(o-1.5/e,2)+.75:o<n?t*(o-=2.25/e)*o+.9375:t*Math.pow(o-2.625/e,2)+.984375};Bi("Bounce",function(a){return 1-s(1-a)},s)})(7.5625,2.75);Bi("Expo",function(t){return Math.pow(2,10*(t-1))*t+t*t*t*t*t*t*(1-t)});Bi("Circ",function(t){return-(uc(1-t*t)-1)});Bi("Sine",function(t){return t===1?1:-g_(t*p_)+1});Bi("Back",Ia("in"),Ia("out"),Ia());ue.SteppedEase=ue.steps=Kt.SteppedEase={config:function(e,r){e===void 0&&(e=1);var i=1/e,n=e+(r?0:1),s=r?1:0,a=1-ye;return function(o){return((n*as(0,a,o)|0)+s)*i}}};rn.ease=ue["quad.out"];It("onComplete,onUpdate,onStart,onRepeat,onReverseComplete,onInterrupt",function(t){return dl+=t+","+t+"Params,"});var Vc=function(e,r){this.id=h_++,e._gsap=this,this.target=e,this.harness=r,this.get=r?r.get:vc,this.set=r?r.getSetter:vl},Zn=function(){function t(r){this.vars=r,this._delay=+r.delay||0,(this._repeat=r.repeat===1/0?-2:r.repeat||0)&&(this._rDelay=r.repeatDelay||0,this._yoyo=!!r.yoyo||!!r.yoyoEase),this._ts=1,an(this,+r.duration,1,1),this.data=r.data,Ce&&(this._ctx=Ce,Ce.data.push(this)),Jn||Wt.wake()}var e=t.prototype;return e.delay=function(i){return i||i===0?(this.parent&&this.parent.smoothChildTiming&&this.startTime(this._start+i-this._delay),this._delay=i,this):this._delay},e.duration=function(i){return arguments.length?this.totalDuration(this._repeat>0?i+(i+this._rDelay)*this._repeat:i):this.totalDuration()&&this._dur},e.totalDuration=function(i){return arguments.length?(this._dirty=0,an(this,this._repeat<0?i:(i-this._repeat*this._rDelay)/(this._repeat+1))):this._tDur},e.totalTime=function(i,n){if(on(),!arguments.length)return this._tTime;var s=this._dp;if(s&&s.smoothChildTiming&&this._ts){for(_a(this,i),!s._dp||s.parent||wc(s,this);s&&s.parent;)s.parent._time!==s._start+(s._ts>=0?s._tTime/s._ts:(s.totalDuration()-s._tTime)/-s._ts)&&s.totalTime(s._tTime,!0),s=s.parent;!this.parent&&this._dp.autoRemoveChildren&&(this._ts>0&&i<this._tDur||this._ts<0&&i>0||!this._tDur&&!i)&&Tr(this._dp,this,this._start-this._delay)}return(this._tTime!==i||!this._dur&&!n||this._initted&&Math.abs(this._zTime)===ye||!i&&!this._initted&&(this.add||this._ptLookup))&&(this._ts||(this._pTime=i),yc(this,i,n)),this},e.time=function(i,n){return arguments.length?this.totalTime(Math.min(this.totalDuration(),i+tu(this))%(this._dur+this._rDelay)||(i?this._dur:0),n):this._time},e.totalProgress=function(i,n){return arguments.length?this.totalTime(this.totalDuration()*i,n):this.totalDuration()?Math.min(1,this._tTime/this._tDur):this.rawTime()>=0&&this._initted?1:0},e.progress=function(i,n){return arguments.length?this.totalTime(this.duration()*(this._yoyo&&!(this.iteration()&1)?1-i:i)+tu(this),n):this.duration()?Math.min(1,this._time/this._dur):this.rawTime()>0?1:0},e.iteration=function(i,n){var s=this.duration()+this._rDelay;return arguments.length?this.totalTime(this._time+(i-1)*s,n):this._repeat?sn(this._tTime,s)+1:1},e.timeScale=function(i,n){if(!arguments.length)return this._rts===-ye?0:this._rts;if(this._rts===i)return this;var s=this.parent&&this._ts?Zs(this.parent._time,this):this._tTime;return this._rts=+i||0,this._ts=this._ps||i===-ye?0:this._rts,this.totalTime(as(-Math.abs(this._delay),this.totalDuration(),s),n!==!1),ga(this),w_(this)},e.paused=function(i){return arguments.length?(this._ps!==i&&(this._ps=i,i?(this._pTime=this._tTime||Math.max(-this._delay,this.rawTime()),this._ts=this._act=0):(on(),this._ts=this._rts,this.totalTime(this.parent&&!this.parent.smoothChildTiming?this.rawTime():this._tTime||this._pTime,this.progress()===1&&Math.abs(this._zTime)!==ye&&(this._tTime-=ye)))),this):this._ps},e.startTime=function(i){if(arguments.length){this._start=i;var n=this.parent||this._dp;return n&&(n._sort||!this.parent)&&Tr(n,this,i-this._delay),this}return this._start},e.endTime=function(i){return this._start+(Dt(i)?this.totalDuration():this.duration())/Math.abs(this._ts||1)},e.rawTime=function(i){var n=this.parent||this._dp;return n?i&&(!this._ts||this._repeat&&this._time&&this.totalProgress()<1)?this._tTime%(this._dur+this._rDelay):this._ts?Zs(n.rawTime(i),this):this._tTime:this._tTime},e.revert=function(i){i===void 0&&(i=y_);var n=ft;return ft=i,hl(this)&&(this.timeline&&this.timeline.revert(i),this.totalTime(-.01,i.suppressEvents)),this.data!=="nested"&&i.kill!==!1&&this.kill(),ft=n,this},e.globalTime=function(i){for(var n=this,s=arguments.length?i:n.rawTime();n;)s=n._start+s/(Math.abs(n._ts)||1),n=n._dp;return!this.parent&&this._sat?this._sat.globalTime(i):s},e.repeat=function(i){return arguments.length?(this._repeat=i===1/0?-2:i,ru(this)):this._repeat===-2?1/0:this._repeat},e.repeatDelay=function(i){if(arguments.length){var n=this._time;return this._rDelay=i,ru(this),n?this.time(n):this}return this._rDelay},e.yoyo=function(i){return arguments.length?(this._yoyo=i,this):this._yoyo},e.seek=function(i,n){return this.totalTime(er(this,i),Dt(n))},e.restart=function(i,n){return this.play().totalTime(i?-this._delay:0,Dt(n)),this._dur||(this._zTime=-ye),this},e.play=function(i,n){return i!=null&&this.seek(i,n),this.reversed(!1).paused(!1)},e.reverse=function(i,n){return i!=null&&this.seek(i||this.totalDuration(),n),this.reversed(!0).paused(!1)},e.pause=function(i,n){return i!=null&&this.seek(i,n),this.paused(!0)},e.resume=function(){return this.paused(!1)},e.reversed=function(i){return arguments.length?(!!i!==this.reversed()&&this.timeScale(-this._rts||(i?-ye:0)),this):this._rts<0},e.invalidate=function(){return this._initted=this._act=0,this._zTime=-ye,this},e.isActive=function(){var i=this.parent||this._dp,n=this._start,s;return!!(!i||this._ts&&this._initted&&i.isActive()&&(s=i.rawTime(!0))>=n&&s<this.endTime(!0)-ye)},e.eventCallback=function(i,n,s){var a=this.vars;return arguments.length>1?(n?(a[i]=n,s&&(a[i+"Params"]=s),i==="onUpdate"&&(this._onUpdate=n)):delete a[i],this):a[i]},e.then=function(i){var n=this;return new Promise(function(s){var a=Re(i)?i:bc,o=function(){var u=n.then;n.then=null,Re(a)&&(a=a(n))&&(a.then||a===n)&&(n.then=u),s(a),n.then=u};n._initted&&n.totalProgress()===1&&n._ts>=0||!n._tTime&&n._ts<0?o():n._prom=o})},e.kill=function(){Cn(this)},t}();Jt(Zn.prototype,{_time:0,_start:0,_end:0,_tTime:0,_tDur:0,_dirty:0,_repeat:0,_yoyo:!1,parent:null,_initted:!1,_rDelay:0,_ts:1,_dp:0,ratio:0,_zTime:-ye,_prom:0,_ps:!1,_rts:1});var Pt=function(t){lc(e,t);function e(i,n){var s;return i===void 0&&(i={}),s=t.call(this,i)||this,s.labels={},s.smoothChildTiming=!!i.smoothChildTiming,s.autoRemoveChildren=!!i.autoRemoveChildren,s._sort=Dt(i.sortChildren),Oe&&Tr(i.parent||Oe,Dr(s),n),i.reversed&&s.reverse(),i.paused&&s.paused(!0),i.scrollTrigger&&Tc(Dr(s),i.scrollTrigger),s}var r=e.prototype;return r.to=function(n,s,a){return zn(0,arguments,this),this},r.from=function(n,s,a){return zn(1,arguments,this),this},r.fromTo=function(n,s,a,o){return zn(2,arguments,this),this},r.set=function(n,s,a){return s.duration=0,s.parent=this,Rn(s).repeatDelay||(s.repeat=0),s.immediateRender=!!s.immediateRender,new Ge(n,s,er(this,a),1),this},r.call=function(n,s,a){return Tr(this,Ge.delayedCall(0,n,s),a)},r.staggerTo=function(n,s,a,o,l,u,f){return a.duration=s,a.stagger=a.stagger||o,a.onComplete=u,a.onCompleteParams=f,a.parent=this,new Ge(n,a,er(this,l)),this},r.staggerFrom=function(n,s,a,o,l,u,f){return a.runBackwards=1,Rn(a).immediateRender=Dt(a.immediateRender),this.staggerTo(n,s,a,o,l,u,f)},r.staggerFromTo=function(n,s,a,o,l,u,f,c){return o.startAt=a,Rn(o).immediateRender=Dt(o.immediateRender),this.staggerTo(n,s,o,l,u,f,c)},r.render=function(n,s,a){var o=this._time,l=this._dirty?this.totalDuration():this._tDur,u=this._dur,f=n<=0?0:He(n),c=this._zTime<0!=n<0&&(this._initted||!u),p,d,_,h,m,g,v,x,y,b,w,C;if(this!==Oe&&f>l&&n>=0&&(f=l),f!==this._tTime||a||c){if(o!==this._time&&u&&(f+=this._time-o,n+=this._time-o),p=f,y=this._start,x=this._ts,g=!x,c&&(u||(o=this._zTime),(n||!s)&&(this._zTime=n)),this._repeat){if(w=this._yoyo,m=u+this._rDelay,this._repeat<-1&&n<0)return this.totalTime(m*100+n,s,a);if(p=He(f%m),f===l?(h=this._repeat,p=u):(b=He(f/m),h=~~b,h&&h===b&&(p=u,h--),p>u&&(p=u)),b=sn(this._tTime,m),!o&&this._tTime&&b!==h&&this._tTime-b*m-this._dur<=0&&(b=h),w&&h&1&&(p=u-p,C=1),h!==b&&!this._lock){var O=w&&b&1,M=O===(w&&h&1);if(h<b&&(O=!O),o=O?0:f%u?u:f,this._lock=1,this.render(o||(C?0:He(h*m)),s,!u)._lock=0,this._tTime=f,!s&&this.parent&&Xt(this,"onRepeat"),this.vars.repeatRefresh&&!C&&(this.invalidate()._lock=1),o&&o!==this._time||g!==!this._ts||this.vars.onRepeat&&!this.parent&&!this._act)return this;if(u=this._dur,l=this._tDur,M&&(this._lock=2,o=O?u:-1e-4,this.render(o,!0),this.vars.repeatRefresh&&!C&&this.invalidate()),this._lock=0,!this._ts&&!g)return this;Bc(this,C)}}if(this._hasPause&&!this._forcing&&this._lock<2&&(v=P_(this,He(o),He(p)),v&&(f-=p-(p=v._start))),this._tTime=f,this._time=p,this._act=!x,this._initted||(this._onUpdate=this.vars.onUpdate,this._initted=1,this._zTime=n,o=0),!o&&f&&!s&&!b&&(Xt(this,"onStart"),this._tTime!==f))return this;if(p>=o&&n>=0)for(d=this._first;d;){if(_=d._next,(d._act||p>=d._start)&&d._ts&&v!==d){if(d.parent!==this)return this.render(n,s,a);if(d.render(d._ts>0?(p-d._start)*d._ts:(d._dirty?d.totalDuration():d._tDur)+(p-d._start)*d._ts,s,a),p!==this._time||!this._ts&&!g){v=0,_&&(f+=this._zTime=-ye);break}}d=_}else{d=this._last;for(var A=n<0?n:p;d;){if(_=d._prev,(d._act||A<=d._end)&&d._ts&&v!==d){if(d.parent!==this)return this.render(n,s,a);if(d.render(d._ts>0?(A-d._start)*d._ts:(d._dirty?d.totalDuration():d._tDur)+(A-d._start)*d._ts,s,a||ft&&hl(d)),p!==this._time||!this._ts&&!g){v=0,_&&(f+=this._zTime=A?-ye:ye);break}}d=_}}if(v&&!s&&(this.pause(),v.render(p>=o?0:-ye)._zTime=p>=o?1:-1,this._ts))return this._start=y,ga(this),this.render(n,s,a);this._onUpdate&&!s&&Xt(this,"onUpdate",!0),(f===l&&this._tTime>=this.totalDuration()||!f&&o)&&(y===this._start||Math.abs(x)!==Math.abs(this._ts))&&(this._lock||((n||!u)&&(f===l&&this._ts>0||!f&&this._ts<0)&&ni(this,1),!s&&!(n<0&&!o)&&(f||o||!l)&&(Xt(this,f===l&&n>=0?"onComplete":"onReverseComplete",!0),this._prom&&!(f<l&&this.timeScale()>0)&&this._prom())))}return this},r.add=function(n,s){var a=this;if(Br(s)||(s=er(this,s,n)),!(n instanceof Zn)){if(yt(n))return n.forEach(function(o){return a.add(o,s)}),this;if(st(n))return this.addLabel(n,s);if(Re(n))n=Ge.delayedCall(0,n);else return this}return this!==n?Tr(this,n,s):this},r.getChildren=function(n,s,a,o){n===void 0&&(n=!0),s===void 0&&(s=!0),a===void 0&&(a=!0),o===void 0&&(o=-nr);for(var l=[],u=this._first;u;)u._start>=o&&(u instanceof Ge?s&&l.push(u):(a&&l.push(u),n&&l.push.apply(l,u.getChildren(!0,s,a)))),u=u._next;return l},r.getById=function(n){for(var s=this.getChildren(1,1,1),a=s.length;a--;)if(s[a].vars.id===n)return s[a]},r.remove=function(n){return st(n)?this.removeLabel(n):Re(n)?this.killTweensOf(n):(n.parent===this&&ha(this,n),n===this._recent&&(this._recent=this._last),Ei(this))},r.totalTime=function(n,s){return arguments.length?(this._forcing=1,!this._dp&&this._ts&&(this._start=He(Wt.time-(this._ts>0?n/this._ts:(this.totalDuration()-n)/-this._ts))),t.prototype.totalTime.call(this,n,s),this._forcing=0,this):this._tTime},r.addLabel=function(n,s){return this.labels[n]=er(this,s),this},r.removeLabel=function(n){return delete this.labels[n],this},r.addPause=function(n,s,a){var o=Ge.delayedCall(0,s||Un,a);return o.data="isPause",this._hasPause=1,Tr(this,o,er(this,n))},r.removePause=function(n){var s=this._first;for(n=er(this,n);s;)s._start===n&&s.data==="isPause"&&ni(s),s=s._next},r.killTweensOf=function(n,s,a){for(var o=this.getTweensOf(n,a),l=o.length;l--;)Xr!==o[l]&&o[l].kill(n,s);return this},r.getTweensOf=function(n,s){for(var a=[],o=sr(n),l=this._first,u=Br(s),f;l;)l instanceof Ge?x_(l._targets,o)&&(u?(!Xr||l._initted&&l._ts)&&l.globalTime(0)<=s&&l.globalTime(l.totalDuration())>s:!s||l.isActive())&&a.push(l):(f=l.getTweensOf(o,s)).length&&a.push.apply(a,f),l=l._next;return a},r.tweenTo=function(n,s){s=s||{};var a=this,o=er(a,n),l=s,u=l.startAt,f=l.onStart,c=l.onStartParams,p=l.immediateRender,d,_=Ge.to(a,Jt({ease:s.ease||"none",lazy:!1,immediateRender:!1,time:o,overwrite:"auto",duration:s.duration||Math.abs((o-(u&&"time"in u?u.time:a._time))/a.timeScale())||ye,onStart:function(){if(a.pause(),!d){var m=s.duration||Math.abs((o-(u&&"time"in u?u.time:a._time))/a.timeScale());_._dur!==m&&an(_,m,0,1).render(_._time,!0,!0),d=1}f&&f.apply(_,c||[])}},s));return p?_.render(0):_},r.tweenFromTo=function(n,s,a){return this.tweenTo(s,Jt({startAt:{time:er(this,n)}},a))},r.recent=function(){return this._recent},r.nextLabel=function(n){return n===void 0&&(n=this._time),iu(this,er(this,n))},r.previousLabel=function(n){return n===void 0&&(n=this._time),iu(this,er(this,n),1)},r.currentLabel=function(n){return arguments.length?this.seek(n,!0):this.previousLabel(this._time+ye)},r.shiftChildren=function(n,s,a){a===void 0&&(a=0);for(var o=this._first,l=this.labels,u;o;)o._start>=a&&(o._start+=n,o._end+=n),o=o._next;if(s)for(u in l)l[u]>=a&&(l[u]+=n);return Ei(this)},r.invalidate=function(n){var s=this._first;for(this._lock=0;s;)s.invalidate(n),s=s._next;return t.prototype.invalidate.call(this,n)},r.clear=function(n){n===void 0&&(n=!0);for(var s=this._first,a;s;)a=s._next,this.remove(s),s=a;return this._dp&&(this._time=this._tTime=this._pTime=0),n&&(this.labels={}),Ei(this)},r.totalDuration=function(n){var s=0,a=this,o=a._last,l=nr,u,f,c;if(arguments.length)return a.timeScale((a._repeat<0?a.duration():a.totalDuration())/(a.reversed()?-n:n));if(a._dirty){for(c=a.parent;o;)u=o._prev,o._dirty&&o.totalDuration(),f=o._start,f>l&&a._sort&&o._ts&&!a._lock?(a._lock=1,Tr(a,o,f-o._delay,1)._lock=0):l=f,f<0&&o._ts&&(s-=f,(!c&&!a._dp||c&&c.smoothChildTiming)&&(a._start+=f/a._ts,a._time-=f,a._tTime-=f),a.shiftChildren(-f,!1,-1/0),l=0),o._end>s&&o._ts&&(s=o._end),o=u;an(a,a===Oe&&a._time>s?a._time:s,1,1),a._dirty=0}return a._tDur},e.updateRoot=function(n){if(Oe._ts&&(yc(Oe,Zs(n,Oe)),mc=Wt.frame),Wt.frame>=Ql){Ql+=Ut.autoSleep||120;var s=Oe._first;if((!s||!s._ts)&&Ut.autoSleep&&Wt._listeners.length<2){for(;s&&!s._ts;)s=s._next;s||Wt.sleep()}}},e}(Zn);Jt(Pt.prototype,{_lock:0,_hasPause:0,_forcing:0});var H_=function(e,r,i,n,s,a,o){var l=new Rt(this._pt,e,r,0,1,qc,null,s),u=0,f=0,c,p,d,_,h,m,g,v;for(l.b=i,l.e=n,i+="",n+="",(g=~n.indexOf("random("))&&(n=Kn(n)),a&&(v=[i,n],a(v,e,r),i=v[0],n=v[1]),p=i.match(Aa)||[];c=Aa.exec(n);)_=c[0],h=n.substring(u,c.index),d?d=(d+1)%5:h.substr(-5)==="rgba("&&(d=1),_!==p[f++]&&(m=parseFloat(p[f-1])||0,l._pt={_next:l._pt,p:h||f===1?h:",",s:m,c:_.charAt(1)==="="?Xi(m,_)-m:parseFloat(_)-m,m:d&&d<4?Math.round:0},u=Aa.lastIndex);return l.c=u<n.length?n.substring(u,n.length):"",l.fp=o,(dc.test(n)||g)&&(l.e=0),this._pt=l,l},gl=function(e,r,i,n,s,a,o,l,u,f){Re(n)&&(n=n(s||0,e,a));var c=e[r],p=i!=="get"?i:Re(c)?u?e[r.indexOf("set")||!Re(e["get"+r.substr(3)])?r:"get"+r.substr(3)](u):e[r]():c,d=Re(c)?u?X_:jc:ml,_;if(st(n)&&(~n.indexOf("random(")&&(n=Kn(n)),n.charAt(1)==="="&&(_=Xi(p,n)+(mt(p)||0),(_||_===0)&&(n=_))),!f||p!==n||bo)return!isNaN(p*n)&&n!==""?(_=new Rt(this._pt,e,r,+p||0,n-(p||0),typeof c=="boolean"?K_:Wc,0,d),u&&(_.fp=u),o&&_.modifier(o,this,e),this._pt=_):(!c&&!(r in e)&&fl(r,n),H_.call(this,e,r,p,n,d,l||Ut.stringFilter,u))},Y_=function(e,r,i,n,s){if(Re(e)&&(e=Fn(e,s,r,i,n)),!Ar(e)||e.style&&e.nodeType||yt(e)||fc(e))return st(e)?Fn(e,s,r,i,n):e;var a={},o;for(o in e)a[o]=Fn(e[o],s,r,i,n);return a},Gc=function(e,r,i,n,s,a){var o,l,u,f;if(Yt[e]&&(o=new Yt[e]).init(s,o.rawVars?r[e]:Y_(r[e],n,s,a,i),i,n,a)!==!1&&(i._pt=l=new Rt(i._pt,s,e,0,1,o.render,o,0,o.priority),i!==Wi))for(u=i._ptLookup[i._targets.indexOf(s)],f=o._props.length;f--;)u[o._props[f]]=l;return o},Xr,bo,_l=function t(e,r,i){var n=e.vars,s=n.ease,a=n.startAt,o=n.immediateRender,l=n.lazy,u=n.onUpdate,f=n.runBackwards,c=n.yoyoEase,p=n.keyframes,d=n.autoRevert,_=e._dur,h=e._startAt,m=e._targets,g=e.parent,v=g&&g.data==="nested"?g.vars.targets:m,x=e._overwrite==="auto"&&!al,y=e.timeline,b,w,C,O,M,A,k,E,L,R,H,F,B;if(y&&(!p||!s)&&(s="none"),e._ease=Pi(s,rn.ease),e._yEase=c?$c(Pi(c===!0?s:c,rn.ease)):0,c&&e._yoyo&&!e._repeat&&(c=e._yEase,e._yEase=e._ease,e._ease=c),e._from=!y&&!!n.runBackwards,!y||p&&!n.stagger){if(E=m[0]?Ci(m[0]).harness:0,F=E&&n[E.prop],b=Js(n,cl),h&&(h._zTime<0&&h.progress(1),r<0&&f&&o&&!d?h.render(-1,!0):h.revert(f&&_?Is:v_),h._lazy=0),a){if(ni(e._startAt=Ge.set(m,Jt({data:"isStart",overwrite:!1,parent:g,immediateRender:!0,lazy:!h&&Dt(l),startAt:null,delay:0,onUpdate:u&&function(){return Xt(e,"onUpdate")},stagger:0},a))),e._startAt._dp=0,e._startAt._sat=e,r<0&&(ft||!o&&!d)&&e._startAt.revert(Is),o&&_&&r<=0&&i<=0){r&&(e._zTime=r);return}}else if(f&&_&&!h){if(r&&(o=!1),C=Jt({overwrite:!1,data:"isFromStart",lazy:o&&!h&&Dt(l),immediateRender:o,stagger:0,parent:g},b),F&&(C[E.prop]=F),ni(e._startAt=Ge.set(m,C)),e._startAt._dp=0,e._startAt._sat=e,r<0&&(ft?e._startAt.revert(Is):e._startAt.render(-1,!0)),e._zTime=r,!o)t(e._startAt,ye,ye);else if(!r)return}for(e._pt=e._ptCache=0,l=_&&Dt(l)||l&&!_,w=0;w<m.length;w++){if(M=m[w],k=M._gsap||pl(m)[w]._gsap,e._ptLookup[w]=R={},go[k.id]&&Qr.length&&Ks(),H=v===m?w:v.indexOf(M),E&&(L=new E).init(M,F||b,e,H,v)!==!1&&(e._pt=O=new Rt(e._pt,M,L.name,0,1,L.render,L,0,L.priority),L._props.forEach(function(j){R[j]=O}),L.priority&&(A=1)),!E||F)for(C in b)Yt[C]&&(L=Gc(C,b,e,H,M,v))?L.priority&&(A=1):R[C]=O=gl.call(e,M,C,"get",b[C],H,v,0,n.stringFilter);e._op&&e._op[w]&&e.kill(M,e._op[w]),x&&e._pt&&(Xr=e,Oe.killTweensOf(M,R,e.globalTime(r)),B=!e.parent,Xr=0),e._pt&&l&&(go[k.id]=1)}A&&Xc(e),e._onInit&&e._onInit(e)}e._onUpdate=u,e._initted=(!e._op||e._pt)&&!B,p&&r<=0&&y.render(nr,!0,!0)},j_=function(e,r,i,n,s,a,o,l){var u=(e._pt&&e._ptCache||(e._ptCache={}))[r],f,c,p,d;if(!u)for(u=e._ptCache[r]=[],p=e._ptLookup,d=e._targets.length;d--;){if(f=p[d][r],f&&f.d&&f.d._pt)for(f=f.d._pt;f&&f.p!==r&&f.fp!==r;)f=f._next;if(!f)return bo=1,e.vars[r]="+=0",_l(e,o),bo=0,l?Xn(r+" not eligible for reset"):1;u.push(f)}for(d=u.length;d--;)c=u[d],f=c._pt||c,f.s=(n||n===0)&&!s?n:f.s+(n||0)+a*f.c,f.c=i-f.s,c.e&&(c.e=$e(i)+mt(c.e)),c.b&&(c.b=f.s+mt(c.b))},W_=function(e,r){var i=e[0]?Ci(e[0]).harness:0,n=i&&i.aliases,s,a,o,l;if(!n)return r;s=nn({},r);for(a in n)if(a in s)for(l=n[a].split(","),o=l.length;o--;)s[l[o]]=s[a];return s},q_=function(e,r,i,n){var s=r.ease||n||"power1.inOut",a,o;if(yt(r))o=i[e]||(i[e]=[]),r.forEach(function(l,u){return o.push({t:u/(r.length-1)*100,v:l,e:s})});else for(a in r)o=i[a]||(i[a]=[]),a==="ease"||o.push({t:parseFloat(e),v:r[a],e:s})},Fn=function(e,r,i,n,s){return Re(e)?e.call(r,i,n,s):st(e)&&~e.indexOf("random(")?Kn(e):e},Hc=dl+"repeat,repeatDelay,yoyo,repeatRefresh,yoyoEase,autoRevert",Yc={};It(Hc+",id,stagger,delay,duration,paused,scrollTrigger",function(t){return Yc[t]=1});var Ge=function(t){lc(e,t);function e(i,n,s,a){var o;typeof n=="number"&&(s.duration=n,n=s,s=null),o=t.call(this,a?n:Rn(n))||this;var l=o.vars,u=l.duration,f=l.delay,c=l.immediateRender,p=l.stagger,d=l.overwrite,_=l.keyframes,h=l.defaults,m=l.scrollTrigger,g=l.yoyoEase,v=n.parent||Oe,x=(yt(i)||fc(i)?Br(i[0]):"length"in n)?[i]:sr(i),y,b,w,C,O,M,A,k;if(o._targets=x.length?pl(x):Xn("GSAP target "+i+" not found. https://gsap.com",!Ut.nullTargetWarn)||[],o._ptLookup=[],o._overwrite=d,_||p||_s(u)||_s(f)){if(n=o.vars,y=o.timeline=new Pt({data:"nested",defaults:h||{},targets:v&&v.data==="nested"?v.vars.targets:x}),y.kill(),y.parent=y._dp=Dr(o),y._start=0,p||_s(u)||_s(f)){if(C=x.length,A=p&&Mc(p),Ar(p))for(O in p)~Hc.indexOf(O)&&(k||(k={}),k[O]=p[O]);for(b=0;b<C;b++)w=Js(n,Yc),w.stagger=0,g&&(w.yoyoEase=g),k&&nn(w,k),M=x[b],w.duration=+Fn(u,Dr(o),b,M,x),w.delay=(+Fn(f,Dr(o),b,M,x)||0)-o._delay,!p&&C===1&&w.delay&&(o._delay=f=w.delay,o._start+=f,w.delay=0),y.to(M,w,A?A(b,M,x):0),y._ease=ue.none;y.duration()?u=f=0:o.timeline=0}else if(_){Rn(Jt(y.vars.defaults,{ease:"none"})),y._ease=Pi(_.ease||n.ease||"none");var E=0,L,R,H;if(yt(_))_.forEach(function(F){return y.to(x,F,">")}),y.duration();else{w={};for(O in _)O==="ease"||O==="easeEach"||q_(O,_[O],w,_.easeEach);for(O in w)for(L=w[O].sort(function(F,B){return F.t-B.t}),E=0,b=0;b<L.length;b++)R=L[b],H={ease:R.e,duration:(R.t-(b?L[b-1].t:0))/100*u},H[O]=R.v,y.to(x,H,E),E+=H.duration;y.duration()<u&&y.to({},{duration:u-y.duration()})}}u||o.duration(u=y.duration())}else o.timeline=0;return d===!0&&!al&&(Xr=Dr(o),Oe.killTweensOf(x),Xr=0),Tr(v,Dr(o),s),n.reversed&&o.reverse(),n.paused&&o.paused(!0),(c||!u&&!_&&o._start===He(v._time)&&Dt(c)&&T_(Dr(o))&&v.data!=="nested")&&(o._tTime=-ye,o.render(Math.max(0,-f)||0)),m&&Tc(Dr(o),m),o}var r=e.prototype;return r.render=function(n,s,a){var o=this._time,l=this._tDur,u=this._dur,f=n<0,c=n>l-ye&&!f?l:n<ye?0:n,p,d,_,h,m,g,v,x,y;if(!u)E_(this,n,s,a);else if(c!==this._tTime||!n||a||!this._initted&&this._tTime||this._startAt&&this._zTime<0!==f||this._lazy){if(p=c,x=this.timeline,this._repeat){if(h=u+this._rDelay,this._repeat<-1&&f)return this.totalTime(h*100+n,s,a);if(p=He(c%h),c===l?(_=this._repeat,p=u):(m=He(c/h),_=~~m,_&&_===m?(p=u,_--):p>u&&(p=u)),g=this._yoyo&&_&1,g&&(y=this._yEase,p=u-p),m=sn(this._tTime,h),p===o&&!a&&this._initted&&_===m)return this._tTime=c,this;_!==m&&(x&&this._yEase&&Bc(x,g),this.vars.repeatRefresh&&!g&&!this._lock&&p!==h&&this._initted&&(this._lock=a=1,this.render(He(h*_),!0).invalidate()._lock=0))}if(!this._initted){if(Cc(this,f?n:p,a,s,c))return this._tTime=0,this;if(o!==this._time&&!(a&&this.vars.repeatRefresh&&_!==m))return this;if(u!==this._dur)return this.render(n,s,a)}if(this._tTime=c,this._time=p,!this._act&&this._ts&&(this._act=1,this._lazy=0),this.ratio=v=(y||this._ease)(p/u),this._from&&(this.ratio=v=1-v),!o&&c&&!s&&!m&&(Xt(this,"onStart"),this._tTime!==c))return this;for(d=this._pt;d;)d.r(v,d.d),d=d._next;x&&x.render(n<0?n:x._dur*x._ease(p/this._dur),s,a)||this._startAt&&(this._zTime=n),this._onUpdate&&!s&&(f&&_o(this,n,s,a),Xt(this,"onUpdate")),this._repeat&&_!==m&&this.vars.onRepeat&&!s&&this.parent&&Xt(this,"onRepeat"),(c===this._tDur||!c)&&this._tTime===c&&(f&&!this._onUpdate&&_o(this,n,!0,!0),(n||!u)&&(c===this._tDur&&this._ts>0||!c&&this._ts<0)&&ni(this,1),!s&&!(f&&!o)&&(c||o||g)&&(Xt(this,c===l?"onComplete":"onReverseComplete",!0),this._prom&&!(c<l&&this.timeScale()>0)&&this._prom()))}return this},r.targets=function(){return this._targets},r.invalidate=function(n){return(!n||!this.vars.runBackwards)&&(this._startAt=0),this._pt=this._op=this._onUpdate=this._lazy=this.ratio=0,this._ptLookup=[],this.timeline&&this.timeline.invalidate(n),t.prototype.invalidate.call(this,n)},r.resetTo=function(n,s,a,o,l){Jn||Wt.wake(),this._ts||this.play();var u=Math.min(this._dur,(this._dp._time-this._start)*this._ts),f;return this._initted||_l(this,u),f=this._ease(u/this._dur),j_(this,n,s,a,o,f,u,l)?this.resetTo(n,s,a,o,1):(_a(this,0),this.parent||Sc(this._dp,this,"_first","_last",this._dp._sort?"_start":0),this.render(0))},r.kill=function(n,s){if(s===void 0&&(s="all"),!n&&(!s||s==="all"))return this._lazy=this._pt=0,this.parent?Cn(this):this.scrollTrigger&&this.scrollTrigger.kill(!!ft),this;if(this.timeline){var a=this.timeline.totalDuration();return this.timeline.killTweensOf(n,s,Xr&&Xr.vars.overwrite!==!0)._first||Cn(this),this.parent&&a!==this.timeline.totalDuration()&&an(this,this._dur*this.timeline._tDur/a,0,1),this}var o=this._targets,l=n?sr(n):o,u=this._ptLookup,f=this._pt,c,p,d,_,h,m,g;if((!s||s==="all")&&S_(o,l))return s==="all"&&(this._pt=0),Cn(this);for(c=this._op=this._op||[],s!=="all"&&(st(s)&&(h={},It(s,function(v){return h[v]=1}),s=h),s=W_(o,s)),g=o.length;g--;)if(~l.indexOf(o[g])){p=u[g],s==="all"?(c[g]=s,_=p,d={}):(d=c[g]=c[g]||{},_=s);for(h in _)m=p&&p[h],m&&((!("kill"in m.d)||m.d.kill(h)===!0)&&ha(this,m,"_pt"),delete p[h]),d!=="all"&&(d[h]=1)}return this._initted&&!this._pt&&f&&Cn(this),this},e.to=function(n,s){return new e(n,s,arguments[2])},e.from=function(n,s){return zn(1,arguments)},e.delayedCall=function(n,s,a,o){return new e(s,0,{immediateRender:!1,lazy:!1,overwrite:!1,delay:n,onComplete:s,onReverseComplete:s,onCompleteParams:a,onReverseCompleteParams:a,callbackScope:o})},e.fromTo=function(n,s,a){return zn(2,arguments)},e.set=function(n,s){return s.duration=0,s.repeatDelay||(s.repeat=0),new e(n,s)},e.killTweensOf=function(n,s,a){return Oe.killTweensOf(n,s,a)},e}(Zn);Jt(Ge.prototype,{_targets:[],_lazy:0,_startAt:0,_op:0,_onInit:0});It("staggerTo,staggerFrom,staggerFromTo",function(t){Ge[t]=function(){var e=new Pt,r=vo.call(arguments,0);return r.splice(t==="staggerFromTo"?5:4,0,0),e[t].apply(e,r)}});var ml=function(e,r,i){return e[r]=i},jc=function(e,r,i){return e[r](i)},X_=function(e,r,i,n){return e[r](n.fp,i)},U_=function(e,r,i){return e.setAttribute(r,i)},vl=function(e,r){return Re(e[r])?jc:ol(e[r])&&e.setAttribute?U_:ml},Wc=function(e,r){return r.set(r.t,r.p,Math.round((r.s+r.c*e)*1e6)/1e6,r)},K_=function(e,r){return r.set(r.t,r.p,!!(r.s+r.c*e),r)},qc=function(e,r){var i=r._pt,n="";if(!e&&r.b)n=r.b;else if(e===1&&r.e)n=r.e;else{for(;i;)n=i.p+(i.m?i.m(i.s+i.c*e):Math.round((i.s+i.c*e)*1e4)/1e4)+n,i=i._next;n+=r.c}r.set(r.t,r.p,n,r)},yl=function(e,r){for(var i=r._pt;i;)i.r(e,i.d),i=i._next},J_=function(e,r,i,n){for(var s=this._pt,a;s;)a=s._next,s.p===n&&s.modifier(e,r,i),s=a},Z_=function(e){for(var r=this._pt,i,n;r;)n=r._next,r.p===e&&!r.op||r.op===e?ha(this,r,"_pt"):r.dep||(i=1),r=n;return!i},Q_=function(e,r,i,n){n.mSet(e,r,n.m.call(n.tween,i,n.mt),n)},Xc=function(e){for(var r=e._pt,i,n,s,a;r;){for(i=r._next,n=s;n&&n.pr>r.pr;)n=n._next;(r._prev=n?n._prev:a)?r._prev._next=r:s=r,(r._next=n)?n._prev=r:a=r,r=i}e._pt=s},Rt=function(){function t(r,i,n,s,a,o,l,u,f){this.t=i,this.s=s,this.c=a,this.p=n,this.r=o||Wc,this.d=l||this,this.set=u||ml,this.pr=f||0,this._next=r,r&&(r._prev=this)}var e=t.prototype;return e.modifier=function(i,n,s){this.mSet=this.mSet||this.set,this.set=Q_,this.m=i,this.mt=s,this.tween=n},t}();It(dl+"parent,duration,ease,delay,overwrite,runBackwards,startAt,yoyo,immediateRender,repeat,repeatDelay,data,paused,reversed,lazy,callbackScope,stringFilter,id,yoyoEase,stagger,inherit,repeatRefresh,keyframes,autoRevert,scrollTrigger",function(t){return cl[t]=1});Kt.TweenMax=Kt.TweenLite=Ge;Kt.TimelineLite=Kt.TimelineMax=Pt;Oe=new Pt({sortChildren:!1,defaults:rn,autoRemoveChildren:!0,id:"root",smoothChildTiming:!0});Ut.stringFilter=Fc;var Mi=[],zs={},em=[],su=0,tm=0,Ra=function(e){return(zs[e]||em).map(function(r){return r()})},So=function(){var e=Date.now(),r=[];e-su>2&&(Ra("matchMediaInit"),Mi.forEach(function(i){var n=i.queries,s=i.conditions,a,o,l,u;for(o in n)a=br.matchMedia(n[o]).matches,a&&(l=1),a!==s[o]&&(s[o]=a,u=1);u&&(i.revert(),l&&r.push(i))}),Ra("matchMediaRevert"),r.forEach(function(i){return i.onMatch(i,function(n){return i.add(null,n)})}),su=e,Ra("matchMedia"))},Uc=function(){function t(r,i){this.selector=i&&yo(i),this.data=[],this._r=[],this.isReverted=!1,this.id=tm++,r&&this.add(r)}var e=t.prototype;return e.add=function(i,n,s){Re(i)&&(s=n,n=i,i=Re);var a=this,o=function(){var u=Ce,f=a.selector,c;return u&&u!==a&&u.data.push(a),s&&(a.selector=yo(s)),Ce=a,c=n.apply(a,arguments),Re(c)&&a._r.push(c),Ce=u,a.selector=f,a.isReverted=!1,c};return a.last=o,i===Re?o(a,function(l){return a.add(null,l)}):i?a[i]=o:o},e.ignore=function(i){var n=Ce;Ce=null,i(this),Ce=n},e.getTweens=function(){var i=[];return this.data.forEach(function(n){return n instanceof t?i.push.apply(i,n.getTweens()):n instanceof Ge&&!(n.parent&&n.parent.data==="nested")&&i.push(n)}),i},e.clear=function(){this._r.length=this.data.length=0},e.kill=function(i,n){var s=this;if(i?function(){for(var o=s.getTweens(),l=s.data.length,u;l--;)u=s.data[l],u.data==="isFlip"&&(u.revert(),u.getChildren(!0,!0,!1).forEach(function(f){return o.splice(o.indexOf(f),1)}));for(o.map(function(f){return{g:f._dur||f._delay||f._sat&&!f._sat.vars.immediateRender?f.globalTime(0):-1/0,t:f}}).sort(function(f,c){return c.g-f.g||-1/0}).forEach(function(f){return f.t.revert(i)}),l=s.data.length;l--;)u=s.data[l],u instanceof Pt?u.data!=="nested"&&(u.scrollTrigger&&u.scrollTrigger.revert(),u.kill()):!(u instanceof Ge)&&u.revert&&u.revert(i);s._r.forEach(function(f){return f(i,s)}),s.isReverted=!0}():this.data.forEach(function(o){return o.kill&&o.kill()}),this.clear(),n)for(var a=Mi.length;a--;)Mi[a].id===this.id&&Mi.splice(a,1)},e.revert=function(i){this.kill(i||{})},t}(),rm=function(){function t(r){this.contexts=[],this.scope=r,Ce&&Ce.data.push(this)}var e=t.prototype;return e.add=function(i,n,s){Ar(i)||(i={matches:i});var a=new Uc(0,s||this.scope),o=a.conditions={},l,u,f;Ce&&!a.selector&&(a.selector=Ce.selector),this.contexts.push(a),n=a.add("onMatch",n),a.queries=i;for(u in i)u==="all"?f=1:(l=br.matchMedia(i[u]),l&&(Mi.indexOf(a)<0&&Mi.push(a),(o[u]=l.matches)&&(f=1),l.addListener?l.addListener(So):l.addEventListener("change",So)));return f&&n(a,function(c){return a.add(null,c)}),this},e.revert=function(i){this.kill(i||{})},e.kill=function(i){this.contexts.forEach(function(n){return n.kill(i,!0)})},t}(),Qs={registerPlugin:function(){for(var e=arguments.length,r=new Array(e),i=0;i<e;i++)r[i]=arguments[i];r.forEach(function(n){return Ic(n)})},timeline:function(e){return new Pt(e)},getTweensOf:function(e,r){return Oe.getTweensOf(e,r)},getProperty:function(e,r,i,n){st(e)&&(e=sr(e)[0]);var s=Ci(e||{}).get,a=i?bc:xc;return i==="native"&&(i=""),e&&(r?a((Yt[r]&&Yt[r].get||s)(e,r,i,n)):function(o,l,u){return a((Yt[o]&&Yt[o].get||s)(e,o,l,u))})},quickSetter:function(e,r,i){if(e=sr(e),e.length>1){var n=e.map(function(f){return Ft.quickSetter(f,r,i)}),s=n.length;return function(f){for(var c=s;c--;)n[c](f)}}e=e[0]||{};var a=Yt[r],o=Ci(e),l=o.harness&&(o.harness.aliases||{})[r]||r,u=a?function(f){var c=new a;Wi._pt=0,c.init(e,i?f+i:f,Wi,0,[e]),c.render(1,c),Wi._pt&&yl(1,Wi)}:o.set(e,l);return a?u:function(f){return u(e,l,i?f+i:f,o,1)}},quickTo:function(e,r,i){var n,s=Ft.to(e,Jt((n={},n[r]="+=0.1",n.paused=!0,n.stagger=0,n),i||{})),a=function(l,u,f){return s.resetTo(r,l,u,f)};return a.tween=s,a},isTweening:function(e){return Oe.getTweensOf(e,!0).length>0},defaults:function(e){return e&&e.ease&&(e.ease=Pi(e.ease,rn.ease)),eu(rn,e||{})},config:function(e){return eu(Ut,e||{})},registerEffect:function(e){var r=e.name,i=e.effect,n=e.plugins,s=e.defaults,a=e.extendTimeline;(n||"").split(",").forEach(function(o){return o&&!Yt[o]&&!Kt[o]&&Xn(r+" effect requires "+o+" plugin.")}),ka[r]=function(o,l,u){return i(sr(o),Jt(l||{},s),u)},a&&(Pt.prototype[r]=function(o,l,u){return this.add(ka[r](o,Ar(l)?l:(u=l)&&{},this),u)})},registerEase:function(e,r){ue[e]=Pi(r)},parseEase:function(e,r){return arguments.length?Pi(e,r):ue},getById:function(e){return Oe.getById(e)},exportRoot:function(e,r){e===void 0&&(e={});var i=new Pt(e),n,s;for(i.smoothChildTiming=Dt(e.smoothChildTiming),Oe.remove(i),i._dp=0,i._time=i._tTime=Oe._time,n=Oe._first;n;)s=n._next,(r||!(!n._dur&&n instanceof Ge&&n.vars.onComplete===n._targets[0]))&&Tr(i,n,n._start-n._delay),n=s;return Tr(Oe,i,0),i},context:function(e,r){return e?new Uc(e,r):Ce},matchMedia:function(e){return new rm(e)},matchMediaRefresh:function(){return Mi.forEach(function(e){var r=e.conditions,i,n;for(n in r)r[n]&&(r[n]=!1,i=1);i&&e.revert()})||So()},addEventListener:function(e,r){var i=zs[e]||(zs[e]=[]);~i.indexOf(r)||i.push(r)},removeEventListener:function(e,r){var i=zs[e],n=i&&i.indexOf(r);n>=0&&i.splice(n,1)},utils:{wrap:I_,wrapYoyo:R_,distribute:Mc,random:Ac,snap:Oc,normalize:D_,getUnit:mt,clamp:O_,splitColor:Rc,toArray:sr,selector:yo,mapRange:Lc,pipe:k_,unitize:L_,interpolate:z_,shuffle:Pc},install:gc,effects:ka,ticker:Wt,updateRoot:Pt.updateRoot,plugins:Yt,globalTimeline:Oe,core:{PropTween:Rt,globals:_c,Tween:Ge,Timeline:Pt,Animation:Zn,getCache:Ci,_removeLinkedListItem:ha,reverting:function(){return ft},context:function(e){return e&&Ce&&(Ce.data.push(e),e._ctx=Ce),Ce},suppressOverwrites:function(e){return al=e}}};It("to,from,fromTo,delayedCall,set,killTweensOf",function(t){return Qs[t]=Ge[t]});Wt.add(Pt.updateRoot);Wi=Qs.to({},{duration:0});var im=function(e,r){for(var i=e._pt;i&&i.p!==r&&i.op!==r&&i.fp!==r;)i=i._next;return i},nm=function(e,r){var i=e._targets,n,s,a;for(n in r)for(s=i.length;s--;)a=e._ptLookup[s][n],a&&(a=a.d)&&(a._pt&&(a=im(a,n)),a&&a.modifier&&a.modifier(r[n],e,i[s],n))},za=function(e,r){return{name:e,headless:1,rawVars:1,init:function(n,s,a){a._onInit=function(o){var l,u;if(st(s)&&(l={},It(s,function(f){return l[f]=1}),s=l),r){l={};for(u in s)l[u]=r(s[u]);s=l}nm(o,s)}}}},Ft=Qs.registerPlugin({name:"attr",init:function(e,r,i,n,s){var a,o,l;this.tween=i;for(a in r)l=e.getAttribute(a)||"",o=this.add(e,"setAttribute",(l||0)+"",r[a],n,s,0,0,a),o.op=a,o.b=l,this._props.push(a)},render:function(e,r){for(var i=r._pt;i;)ft?i.set(i.t,i.p,i.b,i):i.r(e,i.d),i=i._next}},{name:"endArray",headless:1,init:function(e,r){for(var i=r.length;i--;)this.add(e,i,e[i]||0,r[i],0,0,0,0,0,1)}},za("roundProps",xo),za("modifiers"),za("snap",Oc))||Qs;Ge.version=Pt.version=Ft.version="3.13.0";hc=1;ll()&&on();ue.Power0;ue.Power1;ue.Power2;ue.Power3;ue.Power4;ue.Linear;ue.Quad;ue.Cubic;ue.Quart;ue.Quint;ue.Strong;ue.Elastic;ue.Back;ue.SteppedEase;ue.Bounce;ue.Sine;ue.Expo;ue.Circ;/*!
 * CSSPlugin 3.13.0
 * https://gsap.com
 *
 * Copyright 2008-2025, GreenSock. All rights reserved.
 * Subject to the terms at https://gsap.com/standard-license
 * @author: Jack Doyle, <EMAIL>
*/var au,Ur,Ui,xl,yi,ou,bl,sm=function(){return typeof window<"u"},Nr={},hi=180/Math.PI,Ki=Math.PI/180,Ni=Math.atan2,lu=1e8,Sl=/([A-Z])/g,am=/(left|right|width|margin|padding|x)/i,om=/[\s,\(]\S/,Er={autoAlpha:"opacity,visibility",scale:"scaleX,scaleY",alpha:"opacity"},wo=function(e,r){return r.set(r.t,r.p,Math.round((r.s+r.c*e)*1e4)/1e4+r.u,r)},lm=function(e,r){return r.set(r.t,r.p,e===1?r.e:Math.round((r.s+r.c*e)*1e4)/1e4+r.u,r)},um=function(e,r){return r.set(r.t,r.p,e?Math.round((r.s+r.c*e)*1e4)/1e4+r.u:r.b,r)},fm=function(e,r){var i=r.s+r.c*e;r.set(r.t,r.p,~~(i+(i<0?-.5:.5))+r.u,r)},Kc=function(e,r){return r.set(r.t,r.p,e?r.e:r.b,r)},Jc=function(e,r){return r.set(r.t,r.p,e!==1?r.b:r.e,r)},cm=function(e,r,i){return e.style[r]=i},dm=function(e,r,i){return e.style.setProperty(r,i)},pm=function(e,r,i){return e._gsap[r]=i},hm=function(e,r,i){return e._gsap.scaleX=e._gsap.scaleY=i},gm=function(e,r,i,n,s){var a=e._gsap;a.scaleX=a.scaleY=i,a.renderTransform(s,a)},_m=function(e,r,i,n,s){var a=e._gsap;a[r]=i,a.renderTransform(s,a)},Ae="transform",zt=Ae+"Origin",mm=function t(e,r){var i=this,n=this.target,s=n.style,a=n._gsap;if(e in Nr&&s){if(this.tfm=this.tfm||{},e!=="transform")e=Er[e]||e,~e.indexOf(",")?e.split(",").forEach(function(o){return i.tfm[o]=Ir(n,o)}):this.tfm[e]=a.x?a[e]:Ir(n,e),e===zt&&(this.tfm.zOrigin=a.zOrigin);else return Er.transform.split(",").forEach(function(o){return t.call(i,o,r)});if(this.props.indexOf(Ae)>=0)return;a.svg&&(this.svgo=n.getAttribute("data-svg-origin"),this.props.push(zt,r,"")),e=Ae}(s||r)&&this.props.push(e,r,s[e])},Zc=function(e){e.translate&&(e.removeProperty("translate"),e.removeProperty("scale"),e.removeProperty("rotate"))},vm=function(){var e=this.props,r=this.target,i=r.style,n=r._gsap,s,a;for(s=0;s<e.length;s+=3)e[s+1]?e[s+1]===2?r[e[s]](e[s+2]):r[e[s]]=e[s+2]:e[s+2]?i[e[s]]=e[s+2]:i.removeProperty(e[s].substr(0,2)==="--"?e[s]:e[s].replace(Sl,"-$1").toLowerCase());if(this.tfm){for(a in this.tfm)n[a]=this.tfm[a];n.svg&&(n.renderTransform(),r.setAttribute("data-svg-origin",this.svgo||"")),s=bl(),(!s||!s.isStart)&&!i[Ae]&&(Zc(i),n.zOrigin&&i[zt]&&(i[zt]+=" "+n.zOrigin+"px",n.zOrigin=0,n.renderTransform()),n.uncache=1)}},Qc=function(e,r){var i={target:e,props:[],revert:vm,save:mm};return e._gsap||Ft.core.getCache(e),r&&e.style&&e.nodeType&&r.split(",").forEach(function(n){return i.save(n)}),i},ed,To=function(e,r){var i=Ur.createElementNS?Ur.createElementNS((r||"http://www.w3.org/1999/xhtml").replace(/^https/,"http"),e):Ur.createElement(e);return i&&i.style?i:Ur.createElement(e)},ar=function t(e,r,i){var n=getComputedStyle(e);return n[r]||n.getPropertyValue(r.replace(Sl,"-$1").toLowerCase())||n.getPropertyValue(r)||!i&&t(e,ln(r)||r,1)||""},uu="O,Moz,ms,Ms,Webkit".split(","),ln=function(e,r,i){var n=r||yi,s=n.style,a=5;if(e in s&&!i)return e;for(e=e.charAt(0).toUpperCase()+e.substr(1);a--&&!(uu[a]+e in s););return a<0?null:(a===3?"ms":a>=0?uu[a]:"")+e},Co=function(){sm()&&window.document&&(au=window,Ur=au.document,Ui=Ur.documentElement,yi=To("div")||{style:{}},To("div"),Ae=ln(Ae),zt=Ae+"Origin",yi.style.cssText="border-width:0;line-height:0;position:absolute;padding:0",ed=!!ln("perspective"),bl=Ft.core.reverting,xl=1)},fu=function(e){var r=e.ownerSVGElement,i=To("svg",r&&r.getAttribute("xmlns")||"http://www.w3.org/2000/svg"),n=e.cloneNode(!0),s;n.style.display="block",i.appendChild(n),Ui.appendChild(i);try{s=n.getBBox()}catch{}return i.removeChild(n),Ui.removeChild(i),s},cu=function(e,r){for(var i=r.length;i--;)if(e.hasAttribute(r[i]))return e.getAttribute(r[i])},td=function(e){var r,i;try{r=e.getBBox()}catch{r=fu(e),i=1}return r&&(r.width||r.height)||i||(r=fu(e)),r&&!r.width&&!r.x&&!r.y?{x:+cu(e,["x","cx","x1"])||0,y:+cu(e,["y","cy","y1"])||0,width:0,height:0}:r},rd=function(e){return!!(e.getCTM&&(!e.parentNode||e.ownerSVGElement)&&td(e))},Di=function(e,r){if(r){var i=e.style,n;r in Nr&&r!==zt&&(r=Ae),i.removeProperty?(n=r.substr(0,2),(n==="ms"||r.substr(0,6)==="webkit")&&(r="-"+r),i.removeProperty(n==="--"?r:r.replace(Sl,"-$1").toLowerCase())):i.removeAttribute(r)}},Kr=function(e,r,i,n,s,a){var o=new Rt(e._pt,r,i,0,1,a?Jc:Kc);return e._pt=o,o.b=n,o.e=s,e._props.push(i),o},du={deg:1,rad:1,turn:1},ym={grid:1,flex:1},si=function t(e,r,i,n){var s=parseFloat(i)||0,a=(i+"").trim().substr((s+"").length)||"px",o=yi.style,l=am.test(r),u=e.tagName.toLowerCase()==="svg",f=(u?"client":"offset")+(l?"Width":"Height"),c=100,p=n==="px",d=n==="%",_,h,m,g;if(n===a||!s||du[n]||du[a])return s;if(a!=="px"&&!p&&(s=t(e,r,i,"px")),g=e.getCTM&&rd(e),(d||a==="%")&&(Nr[r]||~r.indexOf("adius")))return _=g?e.getBBox()[l?"width":"height"]:e[f],$e(d?s/_*c:s/100*_);if(o[l?"width":"height"]=c+(p?a:n),h=n!=="rem"&&~r.indexOf("adius")||n==="em"&&e.appendChild&&!u?e:e.parentNode,g&&(h=(e.ownerSVGElement||{}).parentNode),(!h||h===Ur||!h.appendChild)&&(h=Ur.body),m=h._gsap,m&&d&&m.width&&l&&m.time===Wt.time&&!m.uncache)return $e(s/m.width*c);if(d&&(r==="height"||r==="width")){var v=e.style[r];e.style[r]=c+n,_=e[f],v?e.style[r]=v:Di(e,r)}else(d||a==="%")&&!ym[ar(h,"display")]&&(o.position=ar(e,"position")),h===e&&(o.position="static"),h.appendChild(yi),_=yi[f],h.removeChild(yi),o.position="absolute";return l&&d&&(m=Ci(h),m.time=Wt.time,m.width=h[f]),$e(p?_*s/c:_&&s?c/_*s:0)},Ir=function(e,r,i,n){var s;return xl||Co(),r in Er&&r!=="transform"&&(r=Er[r],~r.indexOf(",")&&(r=r.split(",")[0])),Nr[r]&&r!=="transform"?(s=es(e,n),s=r!=="transformOrigin"?s[r]:s.svg?s.origin:ta(ar(e,zt))+" "+s.zOrigin+"px"):(s=e.style[r],(!s||s==="auto"||n||~(s+"").indexOf("calc("))&&(s=ea[r]&&ea[r](e,r,i)||ar(e,r)||vc(e,r)||(r==="opacity"?1:0))),i&&!~(s+"").trim().indexOf(" ")?si(e,r,s,i)+i:s},xm=function(e,r,i,n){if(!i||i==="none"){var s=ln(r,e,1),a=s&&ar(e,s,1);a&&a!==i?(r=s,i=a):r==="borderColor"&&(i=ar(e,"borderTopColor"))}var o=new Rt(this._pt,e.style,r,0,1,qc),l=0,u=0,f,c,p,d,_,h,m,g,v,x,y,b;if(o.b=i,o.e=n,i+="",n+="",n.substring(0,6)==="var(--"&&(n=ar(e,n.substring(4,n.indexOf(")")))),n==="auto"&&(h=e.style[r],e.style[r]=n,n=ar(e,r)||n,h?e.style[r]=h:Di(e,r)),f=[i,n],Fc(f),i=f[0],n=f[1],p=i.match(ji)||[],b=n.match(ji)||[],b.length){for(;c=ji.exec(n);)m=c[0],v=n.substring(l,c.index),_?_=(_+1)%5:(v.substr(-5)==="rgba("||v.substr(-5)==="hsla(")&&(_=1),m!==(h=p[u++]||"")&&(d=parseFloat(h)||0,y=h.substr((d+"").length),m.charAt(1)==="="&&(m=Xi(d,m)+y),g=parseFloat(m),x=m.substr((g+"").length),l=ji.lastIndex-x.length,x||(x=x||Ut.units[r]||y,l===n.length&&(n+=x,o.e+=x)),y!==x&&(d=si(e,r,h,x)||0),o._pt={_next:o._pt,p:v||u===1?v:",",s:d,c:g-d,m:_&&_<4||r==="zIndex"?Math.round:0});o.c=l<n.length?n.substring(l,n.length):""}else o.r=r==="display"&&n==="none"?Jc:Kc;return dc.test(n)&&(o.e=0),this._pt=o,o},pu={top:"0%",bottom:"100%",left:"0%",right:"100%",center:"50%"},bm=function(e){var r=e.split(" "),i=r[0],n=r[1]||"50%";return(i==="top"||i==="bottom"||n==="left"||n==="right")&&(e=i,i=n,n=e),r[0]=pu[i]||i,r[1]=pu[n]||n,r.join(" ")},Sm=function(e,r){if(r.tween&&r.tween._time===r.tween._dur){var i=r.t,n=i.style,s=r.u,a=i._gsap,o,l,u;if(s==="all"||s===!0)n.cssText="",l=1;else for(s=s.split(","),u=s.length;--u>-1;)o=s[u],Nr[o]&&(l=1,o=o==="transformOrigin"?zt:Ae),Di(i,o);l&&(Di(i,Ae),a&&(a.svg&&i.removeAttribute("transform"),n.scale=n.rotate=n.translate="none",es(i,1),a.uncache=1,Zc(n)))}},ea={clearProps:function(e,r,i,n,s){if(s.data!=="isFromStart"){var a=e._pt=new Rt(e._pt,r,i,0,0,Sm);return a.u=n,a.pr=-10,a.tween=s,e._props.push(i),1}}},Qn=[1,0,0,1,0,0],id={},nd=function(e){return e==="matrix(1, 0, 0, 1, 0, 0)"||e==="none"||!e},hu=function(e){var r=ar(e,Ae);return nd(r)?Qn:r.substr(7).match(cc).map($e)},wl=function(e,r){var i=e._gsap||Ci(e),n=e.style,s=hu(e),a,o,l,u;return i.svg&&e.getAttribute("transform")?(l=e.transform.baseVal.consolidate().matrix,s=[l.a,l.b,l.c,l.d,l.e,l.f],s.join(",")==="1,0,0,1,0,0"?Qn:s):(s===Qn&&!e.offsetParent&&e!==Ui&&!i.svg&&(l=n.display,n.display="block",a=e.parentNode,(!a||!e.offsetParent&&!e.getBoundingClientRect().width)&&(u=1,o=e.nextElementSibling,Ui.appendChild(e)),s=hu(e),l?n.display=l:Di(e,"display"),u&&(o?a.insertBefore(e,o):a?a.appendChild(e):Ui.removeChild(e))),r&&s.length>6?[s[0],s[1],s[4],s[5],s[12],s[13]]:s)},Eo=function(e,r,i,n,s,a){var o=e._gsap,l=s||wl(e,!0),u=o.xOrigin||0,f=o.yOrigin||0,c=o.xOffset||0,p=o.yOffset||0,d=l[0],_=l[1],h=l[2],m=l[3],g=l[4],v=l[5],x=r.split(" "),y=parseFloat(x[0])||0,b=parseFloat(x[1])||0,w,C,O,M;i?l!==Qn&&(C=d*m-_*h)&&(O=y*(m/C)+b*(-h/C)+(h*v-m*g)/C,M=y*(-_/C)+b*(d/C)-(d*v-_*g)/C,y=O,b=M):(w=td(e),y=w.x+(~x[0].indexOf("%")?y/100*w.width:y),b=w.y+(~(x[1]||x[0]).indexOf("%")?b/100*w.height:b)),n||n!==!1&&o.smooth?(g=y-u,v=b-f,o.xOffset=c+(g*d+v*h)-g,o.yOffset=p+(g*_+v*m)-v):o.xOffset=o.yOffset=0,o.xOrigin=y,o.yOrigin=b,o.smooth=!!n,o.origin=r,o.originIsAbsolute=!!i,e.style[zt]="0px 0px",a&&(Kr(a,o,"xOrigin",u,y),Kr(a,o,"yOrigin",f,b),Kr(a,o,"xOffset",c,o.xOffset),Kr(a,o,"yOffset",p,o.yOffset)),e.setAttribute("data-svg-origin",y+" "+b)},es=function(e,r){var i=e._gsap||new Vc(e);if("x"in i&&!r&&!i.uncache)return i;var n=e.style,s=i.scaleX<0,a="px",o="deg",l=getComputedStyle(e),u=ar(e,zt)||"0",f,c,p,d,_,h,m,g,v,x,y,b,w,C,O,M,A,k,E,L,R,H,F,B,j,Z,S,fe,Le,Ke,_e,De;return f=c=p=h=m=g=v=x=y=0,d=_=1,i.svg=!!(e.getCTM&&rd(e)),l.translate&&((l.translate!=="none"||l.scale!=="none"||l.rotate!=="none")&&(n[Ae]=(l.translate!=="none"?"translate3d("+(l.translate+" 0 0").split(" ").slice(0,3).join(", ")+") ":"")+(l.rotate!=="none"?"rotate("+l.rotate+") ":"")+(l.scale!=="none"?"scale("+l.scale.split(" ").join(",")+") ":"")+(l[Ae]!=="none"?l[Ae]:"")),n.scale=n.rotate=n.translate="none"),C=wl(e,i.svg),i.svg&&(i.uncache?(j=e.getBBox(),u=i.xOrigin-j.x+"px "+(i.yOrigin-j.y)+"px",B=""):B=!r&&e.getAttribute("data-svg-origin"),Eo(e,B||u,!!B||i.originIsAbsolute,i.smooth!==!1,C)),b=i.xOrigin||0,w=i.yOrigin||0,C!==Qn&&(k=C[0],E=C[1],L=C[2],R=C[3],f=H=C[4],c=F=C[5],C.length===6?(d=Math.sqrt(k*k+E*E),_=Math.sqrt(R*R+L*L),h=k||E?Ni(E,k)*hi:0,v=L||R?Ni(L,R)*hi+h:0,v&&(_*=Math.abs(Math.cos(v*Ki))),i.svg&&(f-=b-(b*k+w*L),c-=w-(b*E+w*R))):(De=C[6],Ke=C[7],S=C[8],fe=C[9],Le=C[10],_e=C[11],f=C[12],c=C[13],p=C[14],O=Ni(De,Le),m=O*hi,O&&(M=Math.cos(-O),A=Math.sin(-O),B=H*M+S*A,j=F*M+fe*A,Z=De*M+Le*A,S=H*-A+S*M,fe=F*-A+fe*M,Le=De*-A+Le*M,_e=Ke*-A+_e*M,H=B,F=j,De=Z),O=Ni(-L,Le),g=O*hi,O&&(M=Math.cos(-O),A=Math.sin(-O),B=k*M-S*A,j=E*M-fe*A,Z=L*M-Le*A,_e=R*A+_e*M,k=B,E=j,L=Z),O=Ni(E,k),h=O*hi,O&&(M=Math.cos(O),A=Math.sin(O),B=k*M+E*A,j=H*M+F*A,E=E*M-k*A,F=F*M-H*A,k=B,H=j),m&&Math.abs(m)+Math.abs(h)>359.9&&(m=h=0,g=180-g),d=$e(Math.sqrt(k*k+E*E+L*L)),_=$e(Math.sqrt(F*F+De*De)),O=Ni(H,F),v=Math.abs(O)>2e-4?O*hi:0,y=_e?1/(_e<0?-_e:_e):0),i.svg&&(B=e.getAttribute("transform"),i.forceCSS=e.setAttribute("transform","")||!nd(ar(e,Ae)),B&&e.setAttribute("transform",B))),Math.abs(v)>90&&Math.abs(v)<270&&(s?(d*=-1,v+=h<=0?180:-180,h+=h<=0?180:-180):(_*=-1,v+=v<=0?180:-180)),r=r||i.uncache,i.x=f-((i.xPercent=f&&(!r&&i.xPercent||(Math.round(e.offsetWidth/2)===Math.round(-f)?-50:0)))?e.offsetWidth*i.xPercent/100:0)+a,i.y=c-((i.yPercent=c&&(!r&&i.yPercent||(Math.round(e.offsetHeight/2)===Math.round(-c)?-50:0)))?e.offsetHeight*i.yPercent/100:0)+a,i.z=p+a,i.scaleX=$e(d),i.scaleY=$e(_),i.rotation=$e(h)+o,i.rotationX=$e(m)+o,i.rotationY=$e(g)+o,i.skewX=v+o,i.skewY=x+o,i.transformPerspective=y+a,(i.zOrigin=parseFloat(u.split(" ")[2])||!r&&i.zOrigin||0)&&(n[zt]=ta(u)),i.xOffset=i.yOffset=0,i.force3D=Ut.force3D,i.renderTransform=i.svg?Tm:ed?sd:wm,i.uncache=0,i},ta=function(e){return(e=e.split(" "))[0]+" "+e[1]},Fa=function(e,r,i){var n=mt(r);return $e(parseFloat(r)+parseFloat(si(e,"x",i+"px",n)))+n},wm=function(e,r){r.z="0px",r.rotationY=r.rotationX="0deg",r.force3D=0,sd(e,r)},ci="0deg",bn="0px",di=") ",sd=function(e,r){var i=r||this,n=i.xPercent,s=i.yPercent,a=i.x,o=i.y,l=i.z,u=i.rotation,f=i.rotationY,c=i.rotationX,p=i.skewX,d=i.skewY,_=i.scaleX,h=i.scaleY,m=i.transformPerspective,g=i.force3D,v=i.target,x=i.zOrigin,y="",b=g==="auto"&&e&&e!==1||g===!0;if(x&&(c!==ci||f!==ci)){var w=parseFloat(f)*Ki,C=Math.sin(w),O=Math.cos(w),M;w=parseFloat(c)*Ki,M=Math.cos(w),a=Fa(v,a,C*M*-x),o=Fa(v,o,-Math.sin(w)*-x),l=Fa(v,l,O*M*-x+x)}m!==bn&&(y+="perspective("+m+di),(n||s)&&(y+="translate("+n+"%, "+s+"%) "),(b||a!==bn||o!==bn||l!==bn)&&(y+=l!==bn||b?"translate3d("+a+", "+o+", "+l+") ":"translate("+a+", "+o+di),u!==ci&&(y+="rotate("+u+di),f!==ci&&(y+="rotateY("+f+di),c!==ci&&(y+="rotateX("+c+di),(p!==ci||d!==ci)&&(y+="skew("+p+", "+d+di),(_!==1||h!==1)&&(y+="scale("+_+", "+h+di),v.style[Ae]=y||"translate(0, 0)"},Tm=function(e,r){var i=r||this,n=i.xPercent,s=i.yPercent,a=i.x,o=i.y,l=i.rotation,u=i.skewX,f=i.skewY,c=i.scaleX,p=i.scaleY,d=i.target,_=i.xOrigin,h=i.yOrigin,m=i.xOffset,g=i.yOffset,v=i.forceCSS,x=parseFloat(a),y=parseFloat(o),b,w,C,O,M;l=parseFloat(l),u=parseFloat(u),f=parseFloat(f),f&&(f=parseFloat(f),u+=f,l+=f),l||u?(l*=Ki,u*=Ki,b=Math.cos(l)*c,w=Math.sin(l)*c,C=Math.sin(l-u)*-p,O=Math.cos(l-u)*p,u&&(f*=Ki,M=Math.tan(u-f),M=Math.sqrt(1+M*M),C*=M,O*=M,f&&(M=Math.tan(f),M=Math.sqrt(1+M*M),b*=M,w*=M)),b=$e(b),w=$e(w),C=$e(C),O=$e(O)):(b=c,O=p,w=C=0),(x&&!~(a+"").indexOf("px")||y&&!~(o+"").indexOf("px"))&&(x=si(d,"x",a,"px"),y=si(d,"y",o,"px")),(_||h||m||g)&&(x=$e(x+_-(_*b+h*C)+m),y=$e(y+h-(_*w+h*O)+g)),(n||s)&&(M=d.getBBox(),x=$e(x+n/100*M.width),y=$e(y+s/100*M.height)),M="matrix("+b+","+w+","+C+","+O+","+x+","+y+")",d.setAttribute("transform",M),v&&(d.style[Ae]=M)},Cm=function(e,r,i,n,s){var a=360,o=st(s),l=parseFloat(s)*(o&&~s.indexOf("rad")?hi:1),u=l-n,f=n+u+"deg",c,p;return o&&(c=s.split("_")[1],c==="short"&&(u%=a,u!==u%(a/2)&&(u+=u<0?a:-a)),c==="cw"&&u<0?u=(u+a*lu)%a-~~(u/a)*a:c==="ccw"&&u>0&&(u=(u-a*lu)%a-~~(u/a)*a)),e._pt=p=new Rt(e._pt,r,i,n,u,lm),p.e=f,p.u="deg",e._props.push(i),p},gu=function(e,r){for(var i in r)e[i]=r[i];return e},Em=function(e,r,i){var n=gu({},i._gsap),s="perspective,force3D,transformOrigin,svgOrigin",a=i.style,o,l,u,f,c,p,d,_;n.svg?(u=i.getAttribute("transform"),i.setAttribute("transform",""),a[Ae]=r,o=es(i,1),Di(i,Ae),i.setAttribute("transform",u)):(u=getComputedStyle(i)[Ae],a[Ae]=r,o=es(i,1),a[Ae]=u);for(l in Nr)u=n[l],f=o[l],u!==f&&s.indexOf(l)<0&&(d=mt(u),_=mt(f),c=d!==_?si(i,l,u,_):parseFloat(u),p=parseFloat(f),e._pt=new Rt(e._pt,o,l,c,p-c,wo),e._pt.u=_||0,e._props.push(l));gu(o,n)};It("padding,margin,Width,Radius",function(t,e){var r="Top",i="Right",n="Bottom",s="Left",a=(e<3?[r,i,n,s]:[r+s,r+i,n+i,n+s]).map(function(o){return e<2?t+o:"border"+o+t});ea[e>1?"border"+t:t]=function(o,l,u,f,c){var p,d;if(arguments.length<4)return p=a.map(function(_){return Ir(o,_,u)}),d=p.join(" "),d.split(p[0]).length===5?p[0]:d;p=(f+"").split(" "),d={},a.forEach(function(_,h){return d[_]=p[h]=p[h]||p[(h-1)/2|0]}),o.init(l,d,c)}});var ad={name:"css",register:Co,targetTest:function(e){return e.style&&e.nodeType},init:function(e,r,i,n,s){var a=this._props,o=e.style,l=i.vars.startAt,u,f,c,p,d,_,h,m,g,v,x,y,b,w,C,O;xl||Co(),this.styles=this.styles||Qc(e),O=this.styles.props,this.tween=i;for(h in r)if(h!=="autoRound"&&(f=r[h],!(Yt[h]&&Gc(h,r,i,n,e,s)))){if(d=typeof f,_=ea[h],d==="function"&&(f=f.call(i,n,e,s),d=typeof f),d==="string"&&~f.indexOf("random(")&&(f=Kn(f)),_)_(this,e,h,f,i)&&(C=1);else if(h.substr(0,2)==="--")u=(getComputedStyle(e).getPropertyValue(h)+"").trim(),f+="",ei.lastIndex=0,ei.test(u)||(m=mt(u),g=mt(f)),g?m!==g&&(u=si(e,h,u,g)+g):m&&(f+=m),this.add(o,"setProperty",u,f,n,s,0,0,h),a.push(h),O.push(h,0,o[h]);else if(d!=="undefined"){if(l&&h in l?(u=typeof l[h]=="function"?l[h].call(i,n,e,s):l[h],st(u)&&~u.indexOf("random(")&&(u=Kn(u)),mt(u+"")||u==="auto"||(u+=Ut.units[h]||mt(Ir(e,h))||""),(u+"").charAt(1)==="="&&(u=Ir(e,h))):u=Ir(e,h),p=parseFloat(u),v=d==="string"&&f.charAt(1)==="="&&f.substr(0,2),v&&(f=f.substr(2)),c=parseFloat(f),h in Er&&(h==="autoAlpha"&&(p===1&&Ir(e,"visibility")==="hidden"&&c&&(p=0),O.push("visibility",0,o.visibility),Kr(this,o,"visibility",p?"inherit":"hidden",c?"inherit":"hidden",!c)),h!=="scale"&&h!=="transform"&&(h=Er[h],~h.indexOf(",")&&(h=h.split(",")[0]))),x=h in Nr,x){if(this.styles.save(h),d==="string"&&f.substring(0,6)==="var(--"&&(f=ar(e,f.substring(4,f.indexOf(")"))),c=parseFloat(f)),y||(b=e._gsap,b.renderTransform&&!r.parseTransform||es(e,r.parseTransform),w=r.smoothOrigin!==!1&&b.smooth,y=this._pt=new Rt(this._pt,o,Ae,0,1,b.renderTransform,b,0,-1),y.dep=1),h==="scale")this._pt=new Rt(this._pt,b,"scaleY",b.scaleY,(v?Xi(b.scaleY,v+c):c)-b.scaleY||0,wo),this._pt.u=0,a.push("scaleY",h),h+="X";else if(h==="transformOrigin"){O.push(zt,0,o[zt]),f=bm(f),b.svg?Eo(e,f,0,w,0,this):(g=parseFloat(f.split(" ")[2])||0,g!==b.zOrigin&&Kr(this,b,"zOrigin",b.zOrigin,g),Kr(this,o,h,ta(u),ta(f)));continue}else if(h==="svgOrigin"){Eo(e,f,1,w,0,this);continue}else if(h in id){Cm(this,b,h,p,v?Xi(p,v+f):f);continue}else if(h==="smoothOrigin"){Kr(this,b,"smooth",b.smooth,f);continue}else if(h==="force3D"){b[h]=f;continue}else if(h==="transform"){Em(this,f,e);continue}}else h in o||(h=ln(h)||h);if(x||(c||c===0)&&(p||p===0)&&!om.test(f)&&h in o)m=(u+"").substr((p+"").length),c||(c=0),g=mt(f)||(h in Ut.units?Ut.units[h]:m),m!==g&&(p=si(e,h,u,g)),this._pt=new Rt(this._pt,x?b:o,h,p,(v?Xi(p,v+c):c)-p,!x&&(g==="px"||h==="zIndex")&&r.autoRound!==!1?fm:wo),this._pt.u=g||0,m!==g&&g!=="%"&&(this._pt.b=u,this._pt.r=um);else if(h in o)xm.call(this,e,h,u,v?v+f:f);else if(h in e)this.add(e,h,u||e[h],v?v+f:f,n,s);else if(h!=="parseTransform"){fl(h,f);continue}x||(h in o?O.push(h,0,o[h]):typeof e[h]=="function"?O.push(h,2,e[h]()):O.push(h,1,u||e[h])),a.push(h)}}C&&Xc(this)},render:function(e,r){if(r.tween._time||!bl())for(var i=r._pt;i;)i.r(e,i.d),i=i._next;else r.styles.revert()},get:Ir,aliases:Er,getSetter:function(e,r,i){var n=Er[r];return n&&n.indexOf(",")<0&&(r=n),r in Nr&&r!==zt&&(e._gsap.x||Ir(e,"x"))?i&&ou===i?r==="scale"?hm:pm:(ou=i||{})&&(r==="scale"?gm:_m):e.style&&!ol(e.style[r])?cm:~r.indexOf("-")?dm:vl(e,r)},core:{_removeProperty:Di,_getMatrix:wl}};Ft.utils.checkPrefix=ln;Ft.core.getStyleSaver=Qc;(function(t,e,r,i){var n=It(t+","+e+","+r,function(s){Nr[s]=1});It(e,function(s){Ut.units[s]="deg",id[s]=1}),Er[n[13]]=t+","+e,It(i,function(s){var a=s.split(":");Er[a[1]]=n[a[0]]})})("x,y,z,scale,scaleX,scaleY,xPercent,yPercent","rotation,rotationX,rotationY,skewX,skewY","transform,transformOrigin,svgOrigin,force3D,smoothOrigin,transformPerspective","0:translateX,1:translateY,2:translateZ,8:rotate,8:rotationZ,8:rotateZ,9:rotateX,10:rotateY");It("x,y,z,top,right,bottom,left,width,height,fontSize,padding,margin,perspective",function(t){Ut.units[t]="px"});Ft.registerPlugin(ad);var ai=Ft.registerPlugin(ad)||Ft;ai.core.Tween;function _u(t,e){for(var r=0;r<e.length;r++){var i=e[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function Pm(t,e,r){return e&&_u(t.prototype,e),r&&_u(t,r),t}/*!
 * Observer 3.13.0
 * https://gsap.com
 *
 * @license Copyright 2008-2025, GreenSock. All rights reserved.
 * Subject to the terms at https://gsap.com/standard-license
 * @author: Jack Doyle, <EMAIL>
*/var ut,Fs,qt,Jr,Zr,Ji,od,gi,$n,ld,zr,pr,ud,fd=function(){return ut||typeof window<"u"&&(ut=window.gsap)&&ut.registerPlugin&&ut},cd=1,qi=[],se=[],Or=[],Bn=Date.now,Po=function(e,r){return r},Mm=function(){var e=$n.core,r=e.bridge||{},i=e._scrollers,n=e._proxies;i.push.apply(i,se),n.push.apply(n,Or),se=i,Or=n,Po=function(a,o){return r[a](o)}},ti=function(e,r){return~Or.indexOf(e)&&Or[Or.indexOf(e)+1][r]},Nn=function(e){return!!~ld.indexOf(e)},St=function(e,r,i,n,s){return e.addEventListener(r,i,{passive:n!==!1,capture:!!s})},bt=function(e,r,i,n){return e.removeEventListener(r,i,!!n)},ms="scrollLeft",vs="scrollTop",Mo=function(){return zr&&zr.isPressed||se.cache++},ra=function(e,r){var i=function n(s){if(s||s===0){cd&&(qt.history.scrollRestoration="manual");var a=zr&&zr.isPressed;s=n.v=Math.round(s)||(zr&&zr.iOS?1:0),e(s),n.cacheID=se.cache,a&&Po("ss",s)}else(r||se.cache!==n.cacheID||Po("ref"))&&(n.cacheID=se.cache,n.v=e());return n.v+n.offset};return i.offset=0,e&&i},Mt={s:ms,p:"left",p2:"Left",os:"right",os2:"Right",d:"width",d2:"Width",a:"x",sc:ra(function(t){return arguments.length?qt.scrollTo(t,Ue.sc()):qt.pageXOffset||Jr[ms]||Zr[ms]||Ji[ms]||0})},Ue={s:vs,p:"top",p2:"Top",os:"bottom",os2:"Bottom",d:"height",d2:"Height",a:"y",op:Mt,sc:ra(function(t){return arguments.length?qt.scrollTo(Mt.sc(),t):qt.pageYOffset||Jr[vs]||Zr[vs]||Ji[vs]||0})},kt=function(e,r){return(r&&r._ctx&&r._ctx.selector||ut.utils.toArray)(e)[0]||(typeof e=="string"&&ut.config().nullTargetWarn!==!1?console.warn("Element not found:",e):null)},Om=function(e,r){for(var i=r.length;i--;)if(r[i]===e||r[i].contains(e))return!0;return!1},oi=function(e,r){var i=r.s,n=r.sc;Nn(e)&&(e=Jr.scrollingElement||Zr);var s=se.indexOf(e),a=n===Ue.sc?1:2;!~s&&(s=se.push(e)-1),se[s+a]||St(e,"scroll",Mo);var o=se[s+a],l=o||(se[s+a]=ra(ti(e,i),!0)||(Nn(e)?n:ra(function(u){return arguments.length?e[i]=u:e[i]})));return l.target=e,o||(l.smooth=ut.getProperty(e,"scrollBehavior")==="smooth"),l},Oo=function(e,r,i){var n=e,s=e,a=Bn(),o=a,l=r||50,u=Math.max(500,l*3),f=function(_,h){var m=Bn();h||m-a>l?(s=n,n=_,o=a,a=m):i?n+=_:n=s+(_-s)/(m-o)*(a-o)},c=function(){s=n=i?0:n,o=a=0},p=function(_){var h=o,m=s,g=Bn();return(_||_===0)&&_!==n&&f(_),a===o||g-o>u?0:(n+(i?m:-m))/((i?g:a)-h)*1e3};return{update:f,reset:c,getVelocity:p}},Sn=function(e,r){return r&&!e._gsapAllow&&e.preventDefault(),e.changedTouches?e.changedTouches[0]:e},mu=function(e){var r=Math.max.apply(Math,e),i=Math.min.apply(Math,e);return Math.abs(r)>=Math.abs(i)?r:i},dd=function(){$n=ut.core.globals().ScrollTrigger,$n&&$n.core&&Mm()},pd=function(e){return ut=e||fd(),!Fs&&ut&&typeof document<"u"&&document.body&&(qt=window,Jr=document,Zr=Jr.documentElement,Ji=Jr.body,ld=[qt,Jr,Zr,Ji],ut.utils.clamp,ud=ut.core.context||function(){},gi="onpointerenter"in Ji?"pointer":"mouse",od=Be.isTouch=qt.matchMedia&&qt.matchMedia("(hover: none), (pointer: coarse)").matches?1:"ontouchstart"in qt||navigator.maxTouchPoints>0||navigator.msMaxTouchPoints>0?2:0,pr=Be.eventTypes=("ontouchstart"in Zr?"touchstart,touchmove,touchcancel,touchend":"onpointerdown"in Zr?"pointerdown,pointermove,pointercancel,pointerup":"mousedown,mousemove,mouseup,mouseup").split(","),setTimeout(function(){return cd=0},500),dd(),Fs=1),Fs};Mt.op=Ue;se.cache=0;var Be=function(){function t(r){this.init(r)}var e=t.prototype;return e.init=function(i){Fs||pd(ut)||console.warn("Please gsap.registerPlugin(Observer)"),$n||dd();var n=i.tolerance,s=i.dragMinimum,a=i.type,o=i.target,l=i.lineHeight,u=i.debounce,f=i.preventDefault,c=i.onStop,p=i.onStopDelay,d=i.ignore,_=i.wheelSpeed,h=i.event,m=i.onDragStart,g=i.onDragEnd,v=i.onDrag,x=i.onPress,y=i.onRelease,b=i.onRight,w=i.onLeft,C=i.onUp,O=i.onDown,M=i.onChangeX,A=i.onChangeY,k=i.onChange,E=i.onToggleX,L=i.onToggleY,R=i.onHover,H=i.onHoverEnd,F=i.onMove,B=i.ignoreCheck,j=i.isNormalizer,Z=i.onGestureStart,S=i.onGestureEnd,fe=i.onWheel,Le=i.onEnable,Ke=i.onDisable,_e=i.onClick,De=i.scrollSpeed,je=i.capture,Ee=i.allowClicks,Je=i.lockAxis,Ze=i.onLockAxis;this.target=o=kt(o)||Zr,this.vars=i,d&&(d=ut.utils.toArray(d)),n=n||1e-9,s=s||0,_=_||1,De=De||1,a=a||"wheel,touch,pointer",u=u!==!1,l||(l=parseFloat(qt.getComputedStyle(Ji).lineHeight)||22);var lr,Qe,et,oe,Pe,at,xt,P=this,ct=0,I=0,T=i.passive||!f&&i.passive!==!1,D=oi(o,Mt),V=oi(o,Ue),W=D(),U=V(),ie=~a.indexOf("touch")&&!~a.indexOf("pointer")&&pr[0]==="pointerdown",ze=Nn(o),Q=o.ownerDocument||Jr,xe=[0,0,0],ce=[0,0,0],ae=0,We=function(){return ae=Bn()},N=function(Y,de){return(P.event=Y)&&d&&Om(Y.target,d)||de&&ie&&Y.pointerType!=="touch"||B&&B(Y,de)},Vr=function(){P._vx.reset(),P._vy.reset(),Qe.pause(),c&&c(P)},At=function(){var Y=P.deltaX=mu(xe),de=P.deltaY=mu(ce),z=Math.abs(Y)>=n,K=Math.abs(de)>=n;k&&(z||K)&&k(P,Y,de,xe,ce),z&&(b&&P.deltaX>0&&b(P),w&&P.deltaX<0&&w(P),M&&M(P),E&&P.deltaX<0!=ct<0&&E(P),ct=P.deltaX,xe[0]=xe[1]=xe[2]=0),K&&(O&&P.deltaY>0&&O(P),C&&P.deltaY<0&&C(P),A&&A(P),L&&P.deltaY<0!=I<0&&L(P),I=P.deltaY,ce[0]=ce[1]=ce[2]=0),(oe||et)&&(F&&F(P),et&&(m&&et===1&&m(P),v&&v(P),et=0),oe=!1),at&&!(at=!1)&&Ze&&Ze(P),Pe&&(fe(P),Pe=!1),lr=0},$t=function(Y,de,z){xe[z]+=Y,ce[z]+=de,P._vx.update(Y),P._vy.update(de),u?lr||(lr=requestAnimationFrame(At)):At()},kr=function(Y,de){Je&&!xt&&(P.axis=xt=Math.abs(Y)>Math.abs(de)?"x":"y",at=!0),xt!=="y"&&(xe[2]+=Y,P._vx.update(Y,!0)),xt!=="x"&&(ce[2]+=de,P._vy.update(de,!0)),u?lr||(lr=requestAnimationFrame(At)):At()},Qt=function(Y){if(!N(Y,1)){Y=Sn(Y,f);var de=Y.clientX,z=Y.clientY,K=de-P.x,G=z-P.y,J=P.isDragging;P.x=de,P.y=z,(J||(K||G)&&(Math.abs(P.startX-de)>=s||Math.abs(P.startY-z)>=s))&&(et=J?2:1,J||(P.isDragging=!0),kr(K,G))}},ur=P.onPress=function(q){N(q,1)||q&&q.button||(P.axis=xt=null,Qe.pause(),P.isPressed=!0,q=Sn(q),ct=I=0,P.startX=P.x=q.clientX,P.startY=P.y=q.clientY,P._vx.reset(),P._vy.reset(),St(j?o:Q,pr[1],Qt,T,!0),P.deltaX=P.deltaY=0,x&&x(P))},X=P.onRelease=function(q){if(!N(q,1)){bt(j?o:Q,pr[1],Qt,!0);var Y=!isNaN(P.y-P.startY),de=P.isDragging,z=de&&(Math.abs(P.x-P.startX)>3||Math.abs(P.y-P.startY)>3),K=Sn(q);!z&&Y&&(P._vx.reset(),P._vy.reset(),f&&Ee&&ut.delayedCall(.08,function(){if(Bn()-ae>300&&!q.defaultPrevented){if(q.target.click)q.target.click();else if(Q.createEvent){var G=Q.createEvent("MouseEvents");G.initMouseEvent("click",!0,!0,qt,1,K.screenX,K.screenY,K.clientX,K.clientY,!1,!1,!1,!1,0,null),q.target.dispatchEvent(G)}}})),P.isDragging=P.isGesturing=P.isPressed=!1,c&&de&&!j&&Qe.restart(!0),et&&At(),g&&de&&g(P),y&&y(P,z)}},_r=function(Y){return Y.touches&&Y.touches.length>1&&(P.isGesturing=!0)&&Z(Y,P.isDragging)},tt=function(){return(P.isGesturing=!1)||S(P)},pe=function(Y){if(!N(Y)){var de=D(),z=V();$t((de-W)*De,(z-U)*De,1),W=de,U=z,c&&Qe.restart(!0)}},we=function(Y){if(!N(Y)){Y=Sn(Y,f),fe&&(Pe=!0);var de=(Y.deltaMode===1?l:Y.deltaMode===2?qt.innerHeight:1)*_;$t(Y.deltaX*de,Y.deltaY*de,0),c&&!j&&Qe.restart(!0)}},Bt=function(Y){if(!N(Y)){var de=Y.clientX,z=Y.clientY,K=de-P.x,G=z-P.y;P.x=de,P.y=z,oe=!0,c&&Qe.restart(!0),(K||G)&&kr(K,G)}},fr=function(Y){P.event=Y,R(P)},Ne=function(Y){P.event=Y,H(P)},cr=function(Y){return N(Y)||Sn(Y,f)&&_e(P)};Qe=P._dc=ut.delayedCall(p||.25,Vr).pause(),P.deltaX=P.deltaY=0,P._vx=Oo(0,50,!0),P._vy=Oo(0,50,!0),P.scrollX=D,P.scrollY=V,P.isDragging=P.isGesturing=P.isPressed=!1,ud(this),P.enable=function(q){return P.isEnabled||(St(ze?Q:o,"scroll",Mo),a.indexOf("scroll")>=0&&St(ze?Q:o,"scroll",pe,T,je),a.indexOf("wheel")>=0&&St(o,"wheel",we,T,je),(a.indexOf("touch")>=0&&od||a.indexOf("pointer")>=0)&&(St(o,pr[0],ur,T,je),St(Q,pr[2],X),St(Q,pr[3],X),Ee&&St(o,"click",We,!0,!0),_e&&St(o,"click",cr),Z&&St(Q,"gesturestart",_r),S&&St(Q,"gestureend",tt),R&&St(o,gi+"enter",fr),H&&St(o,gi+"leave",Ne),F&&St(o,gi+"move",Bt)),P.isEnabled=!0,P.isDragging=P.isGesturing=P.isPressed=oe=et=!1,P._vx.reset(),P._vy.reset(),W=D(),U=V(),q&&q.type&&ur(q),Le&&Le(P)),P},P.disable=function(){P.isEnabled&&(qi.filter(function(q){return q!==P&&Nn(q.target)}).length||bt(ze?Q:o,"scroll",Mo),P.isPressed&&(P._vx.reset(),P._vy.reset(),bt(j?o:Q,pr[1],Qt,!0)),bt(ze?Q:o,"scroll",pe,je),bt(o,"wheel",we,je),bt(o,pr[0],ur,je),bt(Q,pr[2],X),bt(Q,pr[3],X),bt(o,"click",We,!0),bt(o,"click",cr),bt(Q,"gesturestart",_r),bt(Q,"gestureend",tt),bt(o,gi+"enter",fr),bt(o,gi+"leave",Ne),bt(o,gi+"move",Bt),P.isEnabled=P.isPressed=P.isDragging=!1,Ke&&Ke(P))},P.kill=P.revert=function(){P.disable();var q=qi.indexOf(P);q>=0&&qi.splice(q,1),zr===P&&(zr=0)},qi.push(P),j&&Nn(o)&&(zr=P),P.enable(h)},Pm(t,[{key:"velocityX",get:function(){return this._vx.getVelocity()}},{key:"velocityY",get:function(){return this._vy.getVelocity()}}]),t}();Be.version="3.13.0";Be.create=function(t){return new Be(t)};Be.register=pd;Be.getAll=function(){return qi.slice()};Be.getById=function(t){return qi.filter(function(e){return e.vars.id===t})[0]};fd()&&ut.registerPlugin(Be);/*!
 * ScrollTrigger 3.13.0
 * https://gsap.com
 *
 * @license Copyright 2008-2025, GreenSock. All rights reserved.
 * Subject to the terms at https://gsap.com/standard-license
 * @author: Jack Doyle, <EMAIL>
*/var $,Hi,ne,be,jt,ge,Tl,ia,ts,Vn,Pn,ys,gt,ma,Ao,Ct,vu,yu,Yi,hd,$a,gd,Tt,ko,_d,md,jr,Lo,Cl,Zi,El,na,Do,Ba,xs=1,_t=Date.now,Na=_t(),or=0,Mn=0,xu=function(e,r,i){var n=Gt(e)&&(e.substr(0,6)==="clamp("||e.indexOf("max")>-1);return i["_"+r+"Clamp"]=n,n?e.substr(6,e.length-7):e},bu=function(e,r){return r&&(!Gt(e)||e.substr(0,6)!=="clamp(")?"clamp("+e+")":e},Am=function t(){return Mn&&requestAnimationFrame(t)},Su=function(){return ma=1},wu=function(){return ma=0},Sr=function(e){return e},On=function(e){return Math.round(e*1e5)/1e5||0},vd=function(){return typeof window<"u"},yd=function(){return $||vd()&&($=window.gsap)&&$.registerPlugin&&$},Ii=function(e){return!!~Tl.indexOf(e)},xd=function(e){return(e==="Height"?El:ne["inner"+e])||jt["client"+e]||ge["client"+e]},bd=function(e){return ti(e,"getBoundingClientRect")||(Ii(e)?function(){return Gs.width=ne.innerWidth,Gs.height=El,Gs}:function(){return Rr(e)})},km=function(e,r,i){var n=i.d,s=i.d2,a=i.a;return(a=ti(e,"getBoundingClientRect"))?function(){return a()[n]}:function(){return(r?xd(s):e["client"+s])||0}},Lm=function(e,r){return!r||~Or.indexOf(e)?bd(e):function(){return Gs}},Pr=function(e,r){var i=r.s,n=r.d2,s=r.d,a=r.a;return Math.max(0,(i="scroll"+n)&&(a=ti(e,i))?a()-bd(e)()[s]:Ii(e)?(jt[i]||ge[i])-xd(n):e[i]-e["offset"+n])},bs=function(e,r){for(var i=0;i<Yi.length;i+=3)(!r||~r.indexOf(Yi[i+1]))&&e(Yi[i],Yi[i+1],Yi[i+2])},Gt=function(e){return typeof e=="string"},vt=function(e){return typeof e=="function"},An=function(e){return typeof e=="number"},_i=function(e){return typeof e=="object"},wn=function(e,r,i){return e&&e.progress(r?0:1)&&i&&e.pause()},Va=function(e,r){if(e.enabled){var i=e._ctx?e._ctx.add(function(){return r(e)}):r(e);i&&i.totalTime&&(e.callbackAnimation=i)}},Vi=Math.abs,Sd="left",wd="top",Pl="right",Ml="bottom",Oi="width",Ai="height",Gn="Right",Hn="Left",Yn="Top",jn="Bottom",Ve="padding",tr="margin",un="Width",Ol="Height",Xe="px",rr=function(e){return ne.getComputedStyle(e)},Dm=function(e){var r=rr(e).position;e.style.position=r==="absolute"||r==="fixed"?r:"relative"},Tu=function(e,r){for(var i in r)i in e||(e[i]=r[i]);return e},Rr=function(e,r){var i=r&&rr(e)[Ao]!=="matrix(1, 0, 0, 1, 0, 0)"&&$.to(e,{x:0,y:0,xPercent:0,yPercent:0,rotation:0,rotationX:0,rotationY:0,scale:1,skewX:0,skewY:0}).progress(1),n=e.getBoundingClientRect();return i&&i.progress(0).kill(),n},sa=function(e,r){var i=r.d2;return e["offset"+i]||e["client"+i]||0},Td=function(e){var r=[],i=e.labels,n=e.duration(),s;for(s in i)r.push(i[s]/n);return r},Im=function(e){return function(r){return $.utils.snap(Td(e),r)}},Al=function(e){var r=$.utils.snap(e),i=Array.isArray(e)&&e.slice(0).sort(function(n,s){return n-s});return i?function(n,s,a){a===void 0&&(a=.001);var o;if(!s)return r(n);if(s>0){for(n-=a,o=0;o<i.length;o++)if(i[o]>=n)return i[o];return i[o-1]}else for(o=i.length,n+=a;o--;)if(i[o]<=n)return i[o];return i[0]}:function(n,s,a){a===void 0&&(a=.001);var o=r(n);return!s||Math.abs(o-n)<a||o-n<0==s<0?o:r(s<0?n-e:n+e)}},Rm=function(e){return function(r,i){return Al(Td(e))(r,i.direction)}},Ss=function(e,r,i,n){return i.split(",").forEach(function(s){return e(r,s,n)})},nt=function(e,r,i,n,s){return e.addEventListener(r,i,{passive:!n,capture:!!s})},it=function(e,r,i,n){return e.removeEventListener(r,i,!!n)},ws=function(e,r,i){i=i&&i.wheelHandler,i&&(e(r,"wheel",i),e(r,"touchmove",i))},Cu={startColor:"green",endColor:"red",indent:0,fontSize:"16px",fontWeight:"normal"},Ts={toggleActions:"play",anticipatePin:0},aa={top:0,left:0,center:.5,bottom:1,right:1},$s=function(e,r){if(Gt(e)){var i=e.indexOf("="),n=~i?+(e.charAt(i-1)+1)*parseFloat(e.substr(i+1)):0;~i&&(e.indexOf("%")>i&&(n*=r/100),e=e.substr(0,i-1)),e=n+(e in aa?aa[e]*r:~e.indexOf("%")?parseFloat(e)*r/100:parseFloat(e)||0)}return e},Cs=function(e,r,i,n,s,a,o,l){var u=s.startColor,f=s.endColor,c=s.fontSize,p=s.indent,d=s.fontWeight,_=be.createElement("div"),h=Ii(i)||ti(i,"pinType")==="fixed",m=e.indexOf("scroller")!==-1,g=h?ge:i,v=e.indexOf("start")!==-1,x=v?u:f,y="border-color:"+x+";font-size:"+c+";color:"+x+";font-weight:"+d+";pointer-events:none;white-space:nowrap;font-family:sans-serif,Arial;z-index:1000;padding:4px 8px;border-width:0;border-style:solid;";return y+="position:"+((m||l)&&h?"fixed;":"absolute;"),(m||l||!h)&&(y+=(n===Ue?Pl:Ml)+":"+(a+parseFloat(p))+"px;"),o&&(y+="box-sizing:border-box;text-align:left;width:"+o.offsetWidth+"px;"),_._isStart=v,_.setAttribute("class","gsap-marker-"+e+(r?" marker-"+r:"")),_.style.cssText=y,_.innerText=r||r===0?e+"-"+r:e,g.children[0]?g.insertBefore(_,g.children[0]):g.appendChild(_),_._offset=_["offset"+n.op.d2],Bs(_,0,n,v),_},Bs=function(e,r,i,n){var s={display:"block"},a=i[n?"os2":"p2"],o=i[n?"p2":"os2"];e._isFlipped=n,s[i.a+"Percent"]=n?-100:0,s[i.a]=n?"1px":0,s["border"+a+un]=1,s["border"+o+un]=0,s[i.p]=r+"px",$.set(e,s)},re=[],Io={},rs,Eu=function(){return _t()-or>34&&(rs||(rs=requestAnimationFrame(Fr)))},Gi=function(){(!Tt||!Tt.isPressed||Tt.startX>ge.clientWidth)&&(se.cache++,Tt?rs||(rs=requestAnimationFrame(Fr)):Fr(),or||zi("scrollStart"),or=_t())},Ga=function(){md=ne.innerWidth,_d=ne.innerHeight},kn=function(e){se.cache++,(e===!0||!gt&&!gd&&!be.fullscreenElement&&!be.webkitFullscreenElement&&(!ko||md!==ne.innerWidth||Math.abs(ne.innerHeight-_d)>ne.innerHeight*.25))&&ia.restart(!0)},Ri={},zm=[],Cd=function t(){return it(te,"scrollEnd",t)||xi(!0)},zi=function(e){return Ri[e]&&Ri[e].map(function(r){return r()})||zm},Vt=[],Ed=function(e){for(var r=0;r<Vt.length;r+=5)(!e||Vt[r+4]&&Vt[r+4].query===e)&&(Vt[r].style.cssText=Vt[r+1],Vt[r].getBBox&&Vt[r].setAttribute("transform",Vt[r+2]||""),Vt[r+3].uncache=1)},kl=function(e,r){var i;for(Ct=0;Ct<re.length;Ct++)i=re[Ct],i&&(!r||i._ctx===r)&&(e?i.kill(1):i.revert(!0,!0));na=!0,r&&Ed(r),r||zi("revert")},Pd=function(e,r){se.cache++,(r||!Et)&&se.forEach(function(i){return vt(i)&&i.cacheID++&&(i.rec=0)}),Gt(e)&&(ne.history.scrollRestoration=Cl=e)},Et,ki=0,Pu,Fm=function(){if(Pu!==ki){var e=Pu=ki;requestAnimationFrame(function(){return e===ki&&xi(!0)})}},Md=function(){ge.appendChild(Zi),El=!Tt&&Zi.offsetHeight||ne.innerHeight,ge.removeChild(Zi)},Mu=function(e){return ts(".gsap-marker-start, .gsap-marker-end, .gsap-marker-scroller-start, .gsap-marker-scroller-end").forEach(function(r){return r.style.display=e?"none":"block"})},xi=function(e,r){if(jt=be.documentElement,ge=be.body,Tl=[ne,be,jt,ge],or&&!e&&!na){nt(te,"scrollEnd",Cd);return}Md(),Et=te.isRefreshing=!0,se.forEach(function(n){return vt(n)&&++n.cacheID&&(n.rec=n())});var i=zi("refreshInit");hd&&te.sort(),r||kl(),se.forEach(function(n){vt(n)&&(n.smooth&&(n.target.style.scrollBehavior="auto"),n(0))}),re.slice(0).forEach(function(n){return n.refresh()}),na=!1,re.forEach(function(n){if(n._subPinOffset&&n.pin){var s=n.vars.horizontal?"offsetWidth":"offsetHeight",a=n.pin[s];n.revert(!0,1),n.adjustPinSpacing(n.pin[s]-a),n.refresh()}}),Do=1,Mu(!0),re.forEach(function(n){var s=Pr(n.scroller,n._dir),a=n.vars.end==="max"||n._endClamp&&n.end>s,o=n._startClamp&&n.start>=s;(a||o)&&n.setPositions(o?s-1:n.start,a?Math.max(o?s:n.start+1,s):n.end,!0)}),Mu(!1),Do=0,i.forEach(function(n){return n&&n.render&&n.render(-1)}),se.forEach(function(n){vt(n)&&(n.smooth&&requestAnimationFrame(function(){return n.target.style.scrollBehavior="smooth"}),n.rec&&n(n.rec))}),Pd(Cl,1),ia.pause(),ki++,Et=2,Fr(2),re.forEach(function(n){return vt(n.vars.onRefresh)&&n.vars.onRefresh(n)}),Et=te.isRefreshing=!1,zi("refresh")},Ro=0,Ns=1,Wn,Fr=function(e){if(e===2||!Et&&!na){te.isUpdating=!0,Wn&&Wn.update(0);var r=re.length,i=_t(),n=i-Na>=50,s=r&&re[0].scroll();if(Ns=Ro>s?-1:1,Et||(Ro=s),n&&(or&&!ma&&i-or>200&&(or=0,zi("scrollEnd")),Pn=Na,Na=i),Ns<0){for(Ct=r;Ct-- >0;)re[Ct]&&re[Ct].update(0,n);Ns=1}else for(Ct=0;Ct<r;Ct++)re[Ct]&&re[Ct].update(0,n);te.isUpdating=!1}rs=0},zo=[Sd,wd,Ml,Pl,tr+jn,tr+Gn,tr+Yn,tr+Hn,"display","flexShrink","float","zIndex","gridColumnStart","gridColumnEnd","gridRowStart","gridRowEnd","gridArea","justifySelf","alignSelf","placeSelf","order"],Vs=zo.concat([Oi,Ai,"boxSizing","max"+un,"max"+Ol,"position",tr,Ve,Ve+Yn,Ve+Gn,Ve+jn,Ve+Hn]),$m=function(e,r,i){Qi(i);var n=e._gsap;if(n.spacerIsNative)Qi(n.spacerState);else if(e._gsap.swappedIn){var s=r.parentNode;s&&(s.insertBefore(e,r),s.removeChild(r))}e._gsap.swappedIn=!1},Ha=function(e,r,i,n){if(!e._gsap.swappedIn){for(var s=zo.length,a=r.style,o=e.style,l;s--;)l=zo[s],a[l]=i[l];a.position=i.position==="absolute"?"absolute":"relative",i.display==="inline"&&(a.display="inline-block"),o[Ml]=o[Pl]="auto",a.flexBasis=i.flexBasis||"auto",a.overflow="visible",a.boxSizing="border-box",a[Oi]=sa(e,Mt)+Xe,a[Ai]=sa(e,Ue)+Xe,a[Ve]=o[tr]=o[wd]=o[Sd]="0",Qi(n),o[Oi]=o["max"+un]=i[Oi],o[Ai]=o["max"+Ol]=i[Ai],o[Ve]=i[Ve],e.parentNode!==r&&(e.parentNode.insertBefore(r,e),r.appendChild(e)),e._gsap.swappedIn=!0}},Bm=/([A-Z])/g,Qi=function(e){if(e){var r=e.t.style,i=e.length,n=0,s,a;for((e.t._gsap||$.core.getCache(e.t)).uncache=1;n<i;n+=2)a=e[n+1],s=e[n],a?r[s]=a:r[s]&&r.removeProperty(s.replace(Bm,"-$1").toLowerCase())}},Es=function(e){for(var r=Vs.length,i=e.style,n=[],s=0;s<r;s++)n.push(Vs[s],i[Vs[s]]);return n.t=e,n},Nm=function(e,r,i){for(var n=[],s=e.length,a=i?8:0,o;a<s;a+=2)o=e[a],n.push(o,o in r?r[o]:e[a+1]);return n.t=e.t,n},Gs={left:0,top:0},Ou=function(e,r,i,n,s,a,o,l,u,f,c,p,d,_){vt(e)&&(e=e(l)),Gt(e)&&e.substr(0,3)==="max"&&(e=p+(e.charAt(4)==="="?$s("0"+e.substr(3),i):0));var h=d?d.time():0,m,g,v;if(d&&d.seek(0),isNaN(e)||(e=+e),An(e))d&&(e=$.utils.mapRange(d.scrollTrigger.start,d.scrollTrigger.end,0,p,e)),o&&Bs(o,i,n,!0);else{vt(r)&&(r=r(l));var x=(e||"0").split(" "),y,b,w,C;v=kt(r,l)||ge,y=Rr(v)||{},(!y||!y.left&&!y.top)&&rr(v).display==="none"&&(C=v.style.display,v.style.display="block",y=Rr(v),C?v.style.display=C:v.style.removeProperty("display")),b=$s(x[0],y[n.d]),w=$s(x[1]||"0",i),e=y[n.p]-u[n.p]-f+b+s-w,o&&Bs(o,w,n,i-w<20||o._isStart&&w>20),i-=i-w}if(_&&(l[_]=e||-.001,e<0&&(e=0)),a){var O=e+i,M=a._isStart;m="scroll"+n.d2,Bs(a,O,n,M&&O>20||!M&&(c?Math.max(ge[m],jt[m]):a.parentNode[m])<=O+1),c&&(u=Rr(o),c&&(a.style[n.op.p]=u[n.op.p]-n.op.m-a._offset+Xe))}return d&&v&&(m=Rr(v),d.seek(p),g=Rr(v),d._caScrollDist=m[n.p]-g[n.p],e=e/d._caScrollDist*p),d&&d.seek(h),d?e:Math.round(e)},Vm=/(webkit|moz|length|cssText|inset)/i,Au=function(e,r,i,n){if(e.parentNode!==r){var s=e.style,a,o;if(r===ge){e._stOrig=s.cssText,o=rr(e);for(a in o)!+a&&!Vm.test(a)&&o[a]&&typeof s[a]=="string"&&a!=="0"&&(s[a]=o[a]);s.top=i,s.left=n}else s.cssText=e._stOrig;$.core.getCache(e).uncache=1,r.appendChild(e)}},Od=function(e,r,i){var n=r,s=n;return function(a){var o=Math.round(e());return o!==n&&o!==s&&Math.abs(o-n)>3&&Math.abs(o-s)>3&&(a=o,i&&i()),s=n,n=Math.round(a),n}},Ps=function(e,r,i){var n={};n[r.p]="+="+i,$.set(e,n)},ku=function(e,r){var i=oi(e,r),n="_scroll"+r.p2,s=function a(o,l,u,f,c){var p=a.tween,d=l.onComplete,_={};u=u||i();var h=Od(i,u,function(){p.kill(),a.tween=0});return c=f&&c||0,f=f||o-u,p&&p.kill(),l[n]=o,l.inherit=!1,l.modifiers=_,_[n]=function(){return h(u+f*p.ratio+c*p.ratio*p.ratio)},l.onUpdate=function(){se.cache++,a.tween&&Fr()},l.onComplete=function(){a.tween=0,d&&d.call(p)},p=a.tween=$.to(e,l),p};return e[n]=i,i.wheelHandler=function(){return s.tween&&s.tween.kill()&&(s.tween=0)},nt(e,"wheel",i.wheelHandler),te.isTouch&&nt(e,"touchmove",i.wheelHandler),s},te=function(){function t(r,i){Hi||t.register($)||console.warn("Please gsap.registerPlugin(ScrollTrigger)"),Lo(this),this.init(r,i)}var e=t.prototype;return e.init=function(i,n){if(this.progress=this.start=0,this.vars&&this.kill(!0,!0),!Mn){this.update=this.refresh=this.kill=Sr;return}i=Tu(Gt(i)||An(i)||i.nodeType?{trigger:i}:i,Ts);var s=i,a=s.onUpdate,o=s.toggleClass,l=s.id,u=s.onToggle,f=s.onRefresh,c=s.scrub,p=s.trigger,d=s.pin,_=s.pinSpacing,h=s.invalidateOnRefresh,m=s.anticipatePin,g=s.onScrubComplete,v=s.onSnapComplete,x=s.once,y=s.snap,b=s.pinReparent,w=s.pinSpacer,C=s.containerAnimation,O=s.fastScrollEnd,M=s.preventOverlaps,A=i.horizontal||i.containerAnimation&&i.horizontal!==!1?Mt:Ue,k=!c&&c!==0,E=kt(i.scroller||ne),L=$.core.getCache(E),R=Ii(E),H=("pinType"in i?i.pinType:ti(E,"pinType")||R&&"fixed")==="fixed",F=[i.onEnter,i.onLeave,i.onEnterBack,i.onLeaveBack],B=k&&i.toggleActions.split(" "),j="markers"in i?i.markers:Ts.markers,Z=R?0:parseFloat(rr(E)["border"+A.p2+un])||0,S=this,fe=i.onRefreshInit&&function(){return i.onRefreshInit(S)},Le=km(E,R,A),Ke=Lm(E,R),_e=0,De=0,je=0,Ee=oi(E,A),Je,Ze,lr,Qe,et,oe,Pe,at,xt,P,ct,I,T,D,V,W,U,ie,ze,Q,xe,ce,ae,We,N,Vr,At,$t,kr,Qt,ur,X,_r,tt,pe,we,Bt,fr,Ne;if(S._startClamp=S._endClamp=!1,S._dir=A,m*=45,S.scroller=E,S.scroll=C?C.time.bind(C):Ee,Qe=Ee(),S.vars=i,n=n||i.animation,"refreshPriority"in i&&(hd=1,i.refreshPriority===-9999&&(Wn=S)),L.tweenScroll=L.tweenScroll||{top:ku(E,Ue),left:ku(E,Mt)},S.tweenTo=Je=L.tweenScroll[A.p],S.scrubDuration=function(z){_r=An(z)&&z,_r?X?X.duration(z):X=$.to(n,{ease:"expo",totalProgress:"+=0",inherit:!1,duration:_r,paused:!0,onComplete:function(){return g&&g(S)}}):(X&&X.progress(1).kill(),X=0)},n&&(n.vars.lazy=!1,n._initted&&!S.isReverted||n.vars.immediateRender!==!1&&i.immediateRender!==!1&&n.duration()&&n.render(0,!0,!0),S.animation=n.pause(),n.scrollTrigger=S,S.scrubDuration(c),Qt=0,l||(l=n.vars.id)),y&&((!_i(y)||y.push)&&(y={snapTo:y}),"scrollBehavior"in ge.style&&$.set(R?[ge,jt]:E,{scrollBehavior:"auto"}),se.forEach(function(z){return vt(z)&&z.target===(R?be.scrollingElement||jt:E)&&(z.smooth=!1)}),lr=vt(y.snapTo)?y.snapTo:y.snapTo==="labels"?Im(n):y.snapTo==="labelsDirectional"?Rm(n):y.directional!==!1?function(z,K){return Al(y.snapTo)(z,_t()-De<500?0:K.direction)}:$.utils.snap(y.snapTo),tt=y.duration||{min:.1,max:2},tt=_i(tt)?Vn(tt.min,tt.max):Vn(tt,tt),pe=$.delayedCall(y.delay||_r/2||.1,function(){var z=Ee(),K=_t()-De<500,G=Je.tween;if((K||Math.abs(S.getVelocity())<10)&&!G&&!ma&&_e!==z){var J=(z-oe)/D,rt=n&&!k?n.totalProgress():J,le=K?0:(rt-ur)/(_t()-Pn)*1e3||0,Fe=$.utils.clamp(-J,1-J,Vi(le/2)*le/.185),dt=J+(y.inertia===!1?0:Fe),Ie,Te,me=y,dr=me.onStart,Me=me.onInterrupt,Nt=me.onComplete;if(Ie=lr(dt,S),An(Ie)||(Ie=dt),Te=Math.max(0,Math.round(oe+Ie*D)),z<=Pe&&z>=oe&&Te!==z){if(G&&!G._initted&&G.data<=Vi(Te-z))return;y.inertia===!1&&(Fe=Ie-J),Je(Te,{duration:tt(Vi(Math.max(Vi(dt-rt),Vi(Ie-rt))*.185/le/.05||0)),ease:y.ease||"power3",data:Vi(Te-z),onInterrupt:function(){return pe.restart(!0)&&Me&&Me(S)},onComplete:function(){S.update(),_e=Ee(),n&&!k&&(X?X.resetTo("totalProgress",Ie,n._tTime/n._tDur):n.progress(Ie)),Qt=ur=n&&!k?n.totalProgress():S.progress,v&&v(S),Nt&&Nt(S)}},z,Fe*D,Te-z-Fe*D),dr&&dr(S,Je.tween)}}else S.isActive&&_e!==z&&pe.restart(!0)}).pause()),l&&(Io[l]=S),p=S.trigger=kt(p||d!==!0&&d),Ne=p&&p._gsap&&p._gsap.stRevert,Ne&&(Ne=Ne(S)),d=d===!0?p:kt(d),Gt(o)&&(o={targets:p,className:o}),d&&(_===!1||_===tr||(_=!_&&d.parentNode&&d.parentNode.style&&rr(d.parentNode).display==="flex"?!1:Ve),S.pin=d,Ze=$.core.getCache(d),Ze.spacer?V=Ze.pinState:(w&&(w=kt(w),w&&!w.nodeType&&(w=w.current||w.nativeElement),Ze.spacerIsNative=!!w,w&&(Ze.spacerState=Es(w))),Ze.spacer=ie=w||be.createElement("div"),ie.classList.add("pin-spacer"),l&&ie.classList.add("pin-spacer-"+l),Ze.pinState=V=Es(d)),i.force3D!==!1&&$.set(d,{force3D:!0}),S.spacer=ie=Ze.spacer,kr=rr(d),We=kr[_+A.os2],Q=$.getProperty(d),xe=$.quickSetter(d,A.a,Xe),Ha(d,ie,kr),U=Es(d)),j){I=_i(j)?Tu(j,Cu):Cu,P=Cs("scroller-start",l,E,A,I,0),ct=Cs("scroller-end",l,E,A,I,0,P),ze=P["offset"+A.op.d2];var cr=kt(ti(E,"content")||E);at=this.markerStart=Cs("start",l,cr,A,I,ze,0,C),xt=this.markerEnd=Cs("end",l,cr,A,I,ze,0,C),C&&(fr=$.quickSetter([at,xt],A.a,Xe)),!H&&!(Or.length&&ti(E,"fixedMarkers")===!0)&&(Dm(R?ge:E),$.set([P,ct],{force3D:!0}),Vr=$.quickSetter(P,A.a,Xe),$t=$.quickSetter(ct,A.a,Xe))}if(C){var q=C.vars.onUpdate,Y=C.vars.onUpdateParams;C.eventCallback("onUpdate",function(){S.update(0,0,1),q&&q.apply(C,Y||[])})}if(S.previous=function(){return re[re.indexOf(S)-1]},S.next=function(){return re[re.indexOf(S)+1]},S.revert=function(z,K){if(!K)return S.kill(!0);var G=z!==!1||!S.enabled,J=gt;G!==S.isReverted&&(G&&(we=Math.max(Ee(),S.scroll.rec||0),je=S.progress,Bt=n&&n.progress()),at&&[at,xt,P,ct].forEach(function(rt){return rt.style.display=G?"none":"block"}),G&&(gt=S,S.update(G)),d&&(!b||!S.isActive)&&(G?$m(d,ie,V):Ha(d,ie,rr(d),N)),G||S.update(G),gt=J,S.isReverted=G)},S.refresh=function(z,K,G,J){if(!((gt||!S.enabled)&&!K)){if(d&&z&&or){nt(t,"scrollEnd",Cd);return}!Et&&fe&&fe(S),gt=S,Je.tween&&!G&&(Je.tween.kill(),Je.tween=0),X&&X.pause(),h&&n&&(n.revert({kill:!1}).invalidate(),n.getChildren&&n.getChildren(!0,!0,!1).forEach(function(Gr){return Gr.vars.immediateRender&&Gr.render(0,!0,!0)})),S.isReverted||S.revert(!0,!0),S._subPinOffset=!1;var rt=Le(),le=Ke(),Fe=C?C.duration():Pr(E,A),dt=D<=.01||!D,Ie=0,Te=J||0,me=_i(G)?G.end:i.end,dr=i.endTrigger||p,Me=_i(G)?G.start:i.start||(i.start===0||!p?0:d?"0 0":"0 100%"),Nt=S.pinnedContainer=i.pinnedContainer&&kt(i.pinnedContainer,S),mr=p&&Math.max(0,re.indexOf(S))||0,ot=mr,lt,pt,fi,os,ht,qe,vr,va,Ll,gn,yr,_n,ls;for(j&&_i(G)&&(_n=$.getProperty(P,A.p),ls=$.getProperty(ct,A.p));ot-- >0;)qe=re[ot],qe.end||qe.refresh(0,1)||(gt=S),vr=qe.pin,vr&&(vr===p||vr===d||vr===Nt)&&!qe.isReverted&&(gn||(gn=[]),gn.unshift(qe),qe.revert(!0,!0)),qe!==re[ot]&&(mr--,ot--);for(vt(Me)&&(Me=Me(S)),Me=xu(Me,"start",S),oe=Ou(Me,p,rt,A,Ee(),at,P,S,le,Z,H,Fe,C,S._startClamp&&"_startClamp")||(d?-.001:0),vt(me)&&(me=me(S)),Gt(me)&&!me.indexOf("+=")&&(~me.indexOf(" ")?me=(Gt(Me)?Me.split(" ")[0]:"")+me:(Ie=$s(me.substr(2),rt),me=Gt(Me)?Me:(C?$.utils.mapRange(0,C.duration(),C.scrollTrigger.start,C.scrollTrigger.end,oe):oe)+Ie,dr=p)),me=xu(me,"end",S),Pe=Math.max(oe,Ou(me||(dr?"100% 0":Fe),dr,rt,A,Ee()+Ie,xt,ct,S,le,Z,H,Fe,C,S._endClamp&&"_endClamp"))||-.001,Ie=0,ot=mr;ot--;)qe=re[ot],vr=qe.pin,vr&&qe.start-qe._pinPush<=oe&&!C&&qe.end>0&&(lt=qe.end-(S._startClamp?Math.max(0,qe.start):qe.start),(vr===p&&qe.start-qe._pinPush<oe||vr===Nt)&&isNaN(Me)&&(Ie+=lt*(1-qe.progress)),vr===d&&(Te+=lt));if(oe+=Ie,Pe+=Ie,S._startClamp&&(S._startClamp+=Ie),S._endClamp&&!Et&&(S._endClamp=Pe||-.001,Pe=Math.min(Pe,Pr(E,A))),D=Pe-oe||(oe-=.01)&&.001,dt&&(je=$.utils.clamp(0,1,$.utils.normalize(oe,Pe,we))),S._pinPush=Te,at&&Ie&&(lt={},lt[A.a]="+="+Ie,Nt&&(lt[A.p]="-="+Ee()),$.set([at,xt],lt)),d&&!(Do&&S.end>=Pr(E,A)))lt=rr(d),os=A===Ue,fi=Ee(),ce=parseFloat(Q(A.a))+Te,!Fe&&Pe>1&&(yr=(R?be.scrollingElement||jt:E).style,yr={style:yr,value:yr["overflow"+A.a.toUpperCase()]},R&&rr(ge)["overflow"+A.a.toUpperCase()]!=="scroll"&&(yr.style["overflow"+A.a.toUpperCase()]="scroll")),Ha(d,ie,lt),U=Es(d),pt=Rr(d,!0),va=H&&oi(E,os?Mt:Ue)(),_?(N=[_+A.os2,D+Te+Xe],N.t=ie,ot=_===Ve?sa(d,A)+D+Te:0,ot&&(N.push(A.d,ot+Xe),ie.style.flexBasis!=="auto"&&(ie.style.flexBasis=ot+Xe)),Qi(N),Nt&&re.forEach(function(Gr){Gr.pin===Nt&&Gr.vars.pinSpacing!==!1&&(Gr._subPinOffset=!0)}),H&&Ee(we)):(ot=sa(d,A),ot&&ie.style.flexBasis!=="auto"&&(ie.style.flexBasis=ot+Xe)),H&&(ht={top:pt.top+(os?fi-oe:va)+Xe,left:pt.left+(os?va:fi-oe)+Xe,boxSizing:"border-box",position:"fixed"},ht[Oi]=ht["max"+un]=Math.ceil(pt.width)+Xe,ht[Ai]=ht["max"+Ol]=Math.ceil(pt.height)+Xe,ht[tr]=ht[tr+Yn]=ht[tr+Gn]=ht[tr+jn]=ht[tr+Hn]="0",ht[Ve]=lt[Ve],ht[Ve+Yn]=lt[Ve+Yn],ht[Ve+Gn]=lt[Ve+Gn],ht[Ve+jn]=lt[Ve+jn],ht[Ve+Hn]=lt[Ve+Hn],W=Nm(V,ht,b),Et&&Ee(0)),n?(Ll=n._initted,$a(1),n.render(n.duration(),!0,!0),ae=Q(A.a)-ce+D+Te,At=Math.abs(D-ae)>1,H&&At&&W.splice(W.length-2,2),n.render(0,!0,!0),Ll||n.invalidate(!0),n.parent||n.totalTime(n.totalTime()),$a(0)):ae=D,yr&&(yr.value?yr.style["overflow"+A.a.toUpperCase()]=yr.value:yr.style.removeProperty("overflow-"+A.a));else if(p&&Ee()&&!C)for(pt=p.parentNode;pt&&pt!==ge;)pt._pinOffset&&(oe-=pt._pinOffset,Pe-=pt._pinOffset),pt=pt.parentNode;gn&&gn.forEach(function(Gr){return Gr.revert(!1,!0)}),S.start=oe,S.end=Pe,Qe=et=Et?we:Ee(),!C&&!Et&&(Qe<we&&Ee(we),S.scroll.rec=0),S.revert(!1,!0),De=_t(),pe&&(_e=-1,pe.restart(!0)),gt=0,n&&k&&(n._initted||Bt)&&n.progress()!==Bt&&n.progress(Bt||0,!0).render(n.time(),!0,!0),(dt||je!==S.progress||C||h||n&&!n._initted)&&(n&&!k&&(n._initted||je||n.vars.immediateRender!==!1)&&n.totalProgress(C&&oe<-.001&&!je?$.utils.normalize(oe,Pe,0):je,!0),S.progress=dt||(Qe-oe)/D===je?0:je),d&&_&&(ie._pinOffset=Math.round(S.progress*ae)),X&&X.invalidate(),isNaN(_n)||(_n-=$.getProperty(P,A.p),ls-=$.getProperty(ct,A.p),Ps(P,A,_n),Ps(at,A,_n-(J||0)),Ps(ct,A,ls),Ps(xt,A,ls-(J||0))),dt&&!Et&&S.update(),f&&!Et&&!T&&(T=!0,f(S),T=!1)}},S.getVelocity=function(){return(Ee()-et)/(_t()-Pn)*1e3||0},S.endAnimation=function(){wn(S.callbackAnimation),n&&(X?X.progress(1):n.paused()?k||wn(n,S.direction<0,1):wn(n,n.reversed()))},S.labelToScroll=function(z){return n&&n.labels&&(oe||S.refresh()||oe)+n.labels[z]/n.duration()*D||0},S.getTrailing=function(z){var K=re.indexOf(S),G=S.direction>0?re.slice(0,K).reverse():re.slice(K+1);return(Gt(z)?G.filter(function(J){return J.vars.preventOverlaps===z}):G).filter(function(J){return S.direction>0?J.end<=oe:J.start>=Pe})},S.update=function(z,K,G){if(!(C&&!G&&!z)){var J=Et===!0?we:S.scroll(),rt=z?0:(J-oe)/D,le=rt<0?0:rt>1?1:rt||0,Fe=S.progress,dt,Ie,Te,me,dr,Me,Nt,mr;if(K&&(et=Qe,Qe=C?Ee():J,y&&(ur=Qt,Qt=n&&!k?n.totalProgress():le)),m&&d&&!gt&&!xs&&or&&(!le&&oe<J+(J-et)/(_t()-Pn)*m?le=1e-4:le===1&&Pe>J+(J-et)/(_t()-Pn)*m&&(le=.9999)),le!==Fe&&S.enabled){if(dt=S.isActive=!!le&&le<1,Ie=!!Fe&&Fe<1,Me=dt!==Ie,dr=Me||!!le!=!!Fe,S.direction=le>Fe?1:-1,S.progress=le,dr&&!gt&&(Te=le&&!Fe?0:le===1?1:Fe===1?2:3,k&&(me=!Me&&B[Te+1]!=="none"&&B[Te+1]||B[Te],mr=n&&(me==="complete"||me==="reset"||me in n))),M&&(Me||mr)&&(mr||c||!n)&&(vt(M)?M(S):S.getTrailing(M).forEach(function(fi){return fi.endAnimation()})),k||(X&&!gt&&!xs?(X._dp._time-X._start!==X._time&&X.render(X._dp._time-X._start),X.resetTo?X.resetTo("totalProgress",le,n._tTime/n._tDur):(X.vars.totalProgress=le,X.invalidate().restart())):n&&n.totalProgress(le,!!(gt&&(De||z)))),d){if(z&&_&&(ie.style[_+A.os2]=We),!H)xe(On(ce+ae*le));else if(dr){if(Nt=!z&&le>Fe&&Pe+1>J&&J+1>=Pr(E,A),b)if(!z&&(dt||Nt)){var ot=Rr(d,!0),lt=J-oe;Au(d,ge,ot.top+(A===Ue?lt:0)+Xe,ot.left+(A===Ue?0:lt)+Xe)}else Au(d,ie);Qi(dt||Nt?W:U),At&&le<1&&dt||xe(ce+(le===1&&!Nt?ae:0))}}y&&!Je.tween&&!gt&&!xs&&pe.restart(!0),o&&(Me||x&&le&&(le<1||!Ba))&&ts(o.targets).forEach(function(fi){return fi.classList[dt||x?"add":"remove"](o.className)}),a&&!k&&!z&&a(S),dr&&!gt?(k&&(mr&&(me==="complete"?n.pause().totalProgress(1):me==="reset"?n.restart(!0).pause():me==="restart"?n.restart(!0):n[me]()),a&&a(S)),(Me||!Ba)&&(u&&Me&&Va(S,u),F[Te]&&Va(S,F[Te]),x&&(le===1?S.kill(!1,1):F[Te]=0),Me||(Te=le===1?1:3,F[Te]&&Va(S,F[Te]))),O&&!dt&&Math.abs(S.getVelocity())>(An(O)?O:2500)&&(wn(S.callbackAnimation),X?X.progress(1):wn(n,me==="reverse"?1:!le,1))):k&&a&&!gt&&a(S)}if($t){var pt=C?J/C.duration()*(C._caScrollDist||0):J;Vr(pt+(P._isFlipped?1:0)),$t(pt)}fr&&fr(-J/C.duration()*(C._caScrollDist||0))}},S.enable=function(z,K){S.enabled||(S.enabled=!0,nt(E,"resize",kn),R||nt(E,"scroll",Gi),fe&&nt(t,"refreshInit",fe),z!==!1&&(S.progress=je=0,Qe=et=_e=Ee()),K!==!1&&S.refresh())},S.getTween=function(z){return z&&Je?Je.tween:X},S.setPositions=function(z,K,G,J){if(C){var rt=C.scrollTrigger,le=C.duration(),Fe=rt.end-rt.start;z=rt.start+Fe*z/le,K=rt.start+Fe*K/le}S.refresh(!1,!1,{start:bu(z,G&&!!S._startClamp),end:bu(K,G&&!!S._endClamp)},J),S.update()},S.adjustPinSpacing=function(z){if(N&&z){var K=N.indexOf(A.d)+1;N[K]=parseFloat(N[K])+z+Xe,N[1]=parseFloat(N[1])+z+Xe,Qi(N)}},S.disable=function(z,K){if(S.enabled&&(z!==!1&&S.revert(!0,!0),S.enabled=S.isActive=!1,K||X&&X.pause(),we=0,Ze&&(Ze.uncache=1),fe&&it(t,"refreshInit",fe),pe&&(pe.pause(),Je.tween&&Je.tween.kill()&&(Je.tween=0)),!R)){for(var G=re.length;G--;)if(re[G].scroller===E&&re[G]!==S)return;it(E,"resize",kn),R||it(E,"scroll",Gi)}},S.kill=function(z,K){S.disable(z,K),X&&!K&&X.kill(),l&&delete Io[l];var G=re.indexOf(S);G>=0&&re.splice(G,1),G===Ct&&Ns>0&&Ct--,G=0,re.forEach(function(J){return J.scroller===S.scroller&&(G=1)}),G||Et||(S.scroll.rec=0),n&&(n.scrollTrigger=null,z&&n.revert({kill:!1}),K||n.kill()),at&&[at,xt,P,ct].forEach(function(J){return J.parentNode&&J.parentNode.removeChild(J)}),Wn===S&&(Wn=0),d&&(Ze&&(Ze.uncache=1),G=0,re.forEach(function(J){return J.pin===d&&G++}),G||(Ze.spacer=0)),i.onKill&&i.onKill(S)},re.push(S),S.enable(!1,!1),Ne&&Ne(S),n&&n.add&&!D){var de=S.update;S.update=function(){S.update=de,se.cache++,oe||Pe||S.refresh()},$.delayedCall(.01,S.update),D=.01,oe=Pe=0}else S.refresh();d&&Fm()},t.register=function(i){return Hi||($=i||yd(),vd()&&window.document&&t.enable(),Hi=Mn),Hi},t.defaults=function(i){if(i)for(var n in i)Ts[n]=i[n];return Ts},t.disable=function(i,n){Mn=0,re.forEach(function(a){return a[n?"kill":"disable"](i)}),it(ne,"wheel",Gi),it(be,"scroll",Gi),clearInterval(ys),it(be,"touchcancel",Sr),it(ge,"touchstart",Sr),Ss(it,be,"pointerdown,touchstart,mousedown",Su),Ss(it,be,"pointerup,touchend,mouseup",wu),ia.kill(),bs(it);for(var s=0;s<se.length;s+=3)ws(it,se[s],se[s+1]),ws(it,se[s],se[s+2])},t.enable=function(){if(ne=window,be=document,jt=be.documentElement,ge=be.body,$&&(ts=$.utils.toArray,Vn=$.utils.clamp,Lo=$.core.context||Sr,$a=$.core.suppressOverwrites||Sr,Cl=ne.history.scrollRestoration||"auto",Ro=ne.pageYOffset||0,$.core.globals("ScrollTrigger",t),ge)){Mn=1,Zi=document.createElement("div"),Zi.style.height="100vh",Zi.style.position="absolute",Md(),Am(),Be.register($),t.isTouch=Be.isTouch,jr=Be.isTouch&&/(iPad|iPhone|iPod|Mac)/g.test(navigator.userAgent),ko=Be.isTouch===1,nt(ne,"wheel",Gi),Tl=[ne,be,jt,ge],$.matchMedia?(t.matchMedia=function(u){var f=$.matchMedia(),c;for(c in u)f.add(c,u[c]);return f},$.addEventListener("matchMediaInit",function(){return kl()}),$.addEventListener("matchMediaRevert",function(){return Ed()}),$.addEventListener("matchMedia",function(){xi(0,1),zi("matchMedia")}),$.matchMedia().add("(orientation: portrait)",function(){return Ga(),Ga})):console.warn("Requires GSAP 3.11.0 or later"),Ga(),nt(be,"scroll",Gi);var i=ge.hasAttribute("style"),n=ge.style,s=n.borderTopStyle,a=$.core.Animation.prototype,o,l;for(a.revert||Object.defineProperty(a,"revert",{value:function(){return this.time(-.01,!0)}}),n.borderTopStyle="solid",o=Rr(ge),Ue.m=Math.round(o.top+Ue.sc())||0,Mt.m=Math.round(o.left+Mt.sc())||0,s?n.borderTopStyle=s:n.removeProperty("border-top-style"),i||(ge.setAttribute("style",""),ge.removeAttribute("style")),ys=setInterval(Eu,250),$.delayedCall(.5,function(){return xs=0}),nt(be,"touchcancel",Sr),nt(ge,"touchstart",Sr),Ss(nt,be,"pointerdown,touchstart,mousedown",Su),Ss(nt,be,"pointerup,touchend,mouseup",wu),Ao=$.utils.checkPrefix("transform"),Vs.push(Ao),Hi=_t(),ia=$.delayedCall(.2,xi).pause(),Yi=[be,"visibilitychange",function(){var u=ne.innerWidth,f=ne.innerHeight;be.hidden?(vu=u,yu=f):(vu!==u||yu!==f)&&kn()},be,"DOMContentLoaded",xi,ne,"load",xi,ne,"resize",kn],bs(nt),re.forEach(function(u){return u.enable(0,1)}),l=0;l<se.length;l+=3)ws(it,se[l],se[l+1]),ws(it,se[l],se[l+2])}},t.config=function(i){"limitCallbacks"in i&&(Ba=!!i.limitCallbacks);var n=i.syncInterval;n&&clearInterval(ys)||(ys=n)&&setInterval(Eu,n),"ignoreMobileResize"in i&&(ko=t.isTouch===1&&i.ignoreMobileResize),"autoRefreshEvents"in i&&(bs(it)||bs(nt,i.autoRefreshEvents||"none"),gd=(i.autoRefreshEvents+"").indexOf("resize")===-1)},t.scrollerProxy=function(i,n){var s=kt(i),a=se.indexOf(s),o=Ii(s);~a&&se.splice(a,o?6:2),n&&(o?Or.unshift(ne,n,ge,n,jt,n):Or.unshift(s,n))},t.clearMatchMedia=function(i){re.forEach(function(n){return n._ctx&&n._ctx.query===i&&n._ctx.kill(!0,!0)})},t.isInViewport=function(i,n,s){var a=(Gt(i)?kt(i):i).getBoundingClientRect(),o=a[s?Oi:Ai]*n||0;return s?a.right-o>0&&a.left+o<ne.innerWidth:a.bottom-o>0&&a.top+o<ne.innerHeight},t.positionInViewport=function(i,n,s){Gt(i)&&(i=kt(i));var a=i.getBoundingClientRect(),o=a[s?Oi:Ai],l=n==null?o/2:n in aa?aa[n]*o:~n.indexOf("%")?parseFloat(n)*o/100:parseFloat(n)||0;return s?(a.left+l)/ne.innerWidth:(a.top+l)/ne.innerHeight},t.killAll=function(i){if(re.slice(0).forEach(function(s){return s.vars.id!=="ScrollSmoother"&&s.kill()}),i!==!0){var n=Ri.killAll||[];Ri={},n.forEach(function(s){return s()})}},t}();te.version="3.13.0";te.saveStyles=function(t){return t?ts(t).forEach(function(e){if(e&&e.style){var r=Vt.indexOf(e);r>=0&&Vt.splice(r,5),Vt.push(e,e.style.cssText,e.getBBox&&e.getAttribute("transform"),$.core.getCache(e),Lo())}}):Vt};te.revert=function(t,e){return kl(!t,e)};te.create=function(t,e){return new te(t,e)};te.refresh=function(t){return t?kn(!0):(Hi||te.register())&&xi(!0)};te.update=function(t){return++se.cache&&Fr(t===!0?2:0)};te.clearScrollMemory=Pd;te.maxScroll=function(t,e){return Pr(t,e?Mt:Ue)};te.getScrollFunc=function(t,e){return oi(kt(t),e?Mt:Ue)};te.getById=function(t){return Io[t]};te.getAll=function(){return re.filter(function(t){return t.vars.id!=="ScrollSmoother"})};te.isScrolling=function(){return!!or};te.snapDirectional=Al;te.addEventListener=function(t,e){var r=Ri[t]||(Ri[t]=[]);~r.indexOf(e)||r.push(e)};te.removeEventListener=function(t,e){var r=Ri[t],i=r&&r.indexOf(e);i>=0&&r.splice(i,1)};te.batch=function(t,e){var r=[],i={},n=e.interval||.016,s=e.batchMax||1e9,a=function(u,f){var c=[],p=[],d=$.delayedCall(n,function(){f(c,p),c=[],p=[]}).pause();return function(_){c.length||d.restart(!0),c.push(_.trigger),p.push(_),s<=c.length&&d.progress(1)}},o;for(o in e)i[o]=o.substr(0,2)==="on"&&vt(e[o])&&o!=="onRefreshInit"?a(o,e[o]):e[o];return vt(s)&&(s=s(),nt(te,"refresh",function(){return s=e.batchMax()})),ts(t).forEach(function(l){var u={};for(o in i)u[o]=i[o];u.trigger=l,r.push(te.create(u))}),r};var Lu=function(e,r,i,n){return r>n?e(n):r<0&&e(0),i>n?(n-r)/(i-r):i<0?r/(r-i):1},Ya=function t(e,r){r===!0?e.style.removeProperty("touch-action"):e.style.touchAction=r===!0?"auto":r?"pan-"+r+(Be.isTouch?" pinch-zoom":""):"none",e===jt&&t(ge,r)},Ms={auto:1,scroll:1},Gm=function(e){var r=e.event,i=e.target,n=e.axis,s=(r.changedTouches?r.changedTouches[0]:r).target,a=s._gsap||$.core.getCache(s),o=_t(),l;if(!a._isScrollT||o-a._isScrollT>2e3){for(;s&&s!==ge&&(s.scrollHeight<=s.clientHeight&&s.scrollWidth<=s.clientWidth||!(Ms[(l=rr(s)).overflowY]||Ms[l.overflowX]));)s=s.parentNode;a._isScroll=s&&s!==i&&!Ii(s)&&(Ms[(l=rr(s)).overflowY]||Ms[l.overflowX]),a._isScrollT=o}(a._isScroll||n==="x")&&(r.stopPropagation(),r._gsapAllow=!0)},Ad=function(e,r,i,n){return Be.create({target:e,capture:!0,debounce:!1,lockAxis:!0,type:r,onWheel:n=n&&Gm,onPress:n,onDrag:n,onScroll:n,onEnable:function(){return i&&nt(be,Be.eventTypes[0],Iu,!1,!0)},onDisable:function(){return it(be,Be.eventTypes[0],Iu,!0)}})},Hm=/(input|label|select|textarea)/i,Du,Iu=function(e){var r=Hm.test(e.target.tagName);(r||Du)&&(e._gsapAllow=!0,Du=r)},Ym=function(e){_i(e)||(e={}),e.preventDefault=e.isNormalizer=e.allowClicks=!0,e.type||(e.type="wheel,touch"),e.debounce=!!e.debounce,e.id=e.id||"normalizer";var r=e,i=r.normalizeScrollX,n=r.momentum,s=r.allowNestedScroll,a=r.onRelease,o,l,u=kt(e.target)||jt,f=$.core.globals().ScrollSmoother,c=f&&f.get(),p=jr&&(e.content&&kt(e.content)||c&&e.content!==!1&&!c.smooth()&&c.content()),d=oi(u,Ue),_=oi(u,Mt),h=1,m=(Be.isTouch&&ne.visualViewport?ne.visualViewport.scale*ne.visualViewport.width:ne.outerWidth)/ne.innerWidth,g=0,v=vt(n)?function(){return n(o)}:function(){return n||2.8},x,y,b=Ad(u,e.type,!0,s),w=function(){return y=!1},C=Sr,O=Sr,M=function(){l=Pr(u,Ue),O=Vn(jr?1:0,l),i&&(C=Vn(0,Pr(u,Mt))),x=ki},A=function(){p._gsap.y=On(parseFloat(p._gsap.y)+d.offset)+"px",p.style.transform="matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, "+parseFloat(p._gsap.y)+", 0, 1)",d.offset=d.cacheID=0},k=function(){if(y){requestAnimationFrame(w);var j=On(o.deltaY/2),Z=O(d.v-j);if(p&&Z!==d.v+d.offset){d.offset=Z-d.v;var S=On((parseFloat(p&&p._gsap.y)||0)-d.offset);p.style.transform="matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, "+S+", 0, 1)",p._gsap.y=S+"px",d.cacheID=se.cache,Fr()}return!0}d.offset&&A(),y=!0},E,L,R,H,F=function(){M(),E.isActive()&&E.vars.scrollY>l&&(d()>l?E.progress(1)&&d(l):E.resetTo("scrollY",l))};return p&&$.set(p,{y:"+=0"}),e.ignoreCheck=function(B){return jr&&B.type==="touchmove"&&k()||h>1.05&&B.type!=="touchstart"||o.isGesturing||B.touches&&B.touches.length>1},e.onPress=function(){y=!1;var B=h;h=On((ne.visualViewport&&ne.visualViewport.scale||1)/m),E.pause(),B!==h&&Ya(u,h>1.01?!0:i?!1:"x"),L=_(),R=d(),M(),x=ki},e.onRelease=e.onGestureStart=function(B,j){if(d.offset&&A(),!j)H.restart(!0);else{se.cache++;var Z=v(),S,fe;i&&(S=_(),fe=S+Z*.05*-B.velocityX/.227,Z*=Lu(_,S,fe,Pr(u,Mt)),E.vars.scrollX=C(fe)),S=d(),fe=S+Z*.05*-B.velocityY/.227,Z*=Lu(d,S,fe,Pr(u,Ue)),E.vars.scrollY=O(fe),E.invalidate().duration(Z).play(.01),(jr&&E.vars.scrollY>=l||S>=l-1)&&$.to({},{onUpdate:F,duration:Z})}a&&a(B)},e.onWheel=function(){E._ts&&E.pause(),_t()-g>1e3&&(x=0,g=_t())},e.onChange=function(B,j,Z,S,fe){if(ki!==x&&M(),j&&i&&_(C(S[2]===j?L+(B.startX-B.x):_()+j-S[1])),Z){d.offset&&A();var Le=fe[2]===Z,Ke=Le?R+B.startY-B.y:d()+Z-fe[1],_e=O(Ke);Le&&Ke!==_e&&(R+=_e-Ke),d(_e)}(Z||j)&&Fr()},e.onEnable=function(){Ya(u,i?!1:"x"),te.addEventListener("refresh",F),nt(ne,"resize",F),d.smooth&&(d.target.style.scrollBehavior="auto",d.smooth=_.smooth=!1),b.enable()},e.onDisable=function(){Ya(u,!0),it(ne,"resize",F),te.removeEventListener("refresh",F),b.kill()},e.lockAxis=e.lockAxis!==!1,o=new Be(e),o.iOS=jr,jr&&!d()&&d(1),jr&&$.ticker.add(Sr),H=o._dc,E=$.to(o,{ease:"power4",paused:!0,inherit:!1,scrollX:i?"+=0.1":"+=0",scrollY:"+=0.1",modifiers:{scrollY:Od(d,d(),function(){return E.pause()})},onUpdate:Fr,onComplete:H.vars.onComplete}),o};te.sort=function(t){if(vt(t))return re.sort(t);var e=ne.pageYOffset||0;return te.getAll().forEach(function(r){return r._sortY=r.trigger?e+r.trigger.getBoundingClientRect().top:r.start+ne.innerHeight}),re.sort(t||function(r,i){return(r.vars.refreshPriority||0)*-1e6+(r.vars.containerAnimation?1e6:r._sortY)-((i.vars.containerAnimation?1e6:i._sortY)+(i.vars.refreshPriority||0)*-1e6)})};te.observe=function(t){return new Be(t)};te.normalizeScroll=function(t){if(typeof t>"u")return Tt;if(t===!0&&Tt)return Tt.enable();if(t===!1){Tt&&Tt.kill(),Tt=t;return}var e=t instanceof Be?t:Ym(t);return Tt&&Tt.target===e.target&&Tt.kill(),Ii(e.target)&&(Tt=e),e};te.core={_getVelocityProp:Oo,_inputObserver:Ad,_scrollers:se,_proxies:Or,bridge:{ss:function(){or||zi("scrollStart"),or=_t()},ref:function(){return gt}}};yd()&&$.registerPlugin(te);const jm={init(){ai.registerPlugin(te);const t=document.querySelector(".js-team-slider"),e=t.dataset.lockposition??"10%",r=200;function i(){return-(t.scrollWidth-window.innerWidth+window.innerWidth*20/100)}const n=ai.timeline();n.to(t,{x:i,duration:3,ease:"power4.inOut"}).to({},{duration:1}),te.create({trigger:".js-team-slider-wrapper",start:`top ${e}`,end:()=>`+=${i()*-1+r}`,pin:!0,animation:n,scrub:3,invalidateOnRefresh:!0})}},Wm={init(){console.log("init"),new Lt(".js-mobile-related-perspectives-slider",{slidesPerView:1.3,grabCursor:!1,spaceBetween:32,freeMode:!0,centeredSlides:!1,breakpoints:{1024:{slidesPerView:2.5}}})}},qm={init(){new Lt(".js-services-slider",{modules:[ac,oc],slidesPerView:1,grabCursor:!0,freeMode:!1,centeredSlides:!1,pagination:{el:".swiper-pagination",clickable:!0},autoplay:{delay:2e3,disableOnInteraction:!1},speed:1e3})}},Xm={init(){ai.registerPlugin(te),document.querySelectorAll(".js-fade-in-up").forEach(e=>{ai.to(e,{scrollTrigger:{trigger:e,start:"top 60%"},translateY:0,opacity:1,ease:"sine.in",duration:1})})}},Um={init(){ai.registerPlugin(te);const t=document.querySelector(".content-area-a"),e=document.querySelector(".content-area-b");if(!t||!e)return;const r=()=>{const n=window.innerHeight,a=t.offsetHeight<n;return ai.timeline({scrollTrigger:{trigger:".content-area-a",start:a?"top top":"bottom bottom",end:"bottom top",endTrigger:".content-area-b",pin:".content-area-a",pinSpacing:!1,scrub:!0,markers:!1}})};let i=r();window.addEventListener("resize",()=>{i.scrollTrigger.kill(),i=r()})}};function Ru(t,e){for(var r=0;r<e.length;r++){var i=e[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function Km(t,e,r){return e&&Ru(t.prototype,e),r&&Ru(t,r),t}/*!
 * ScrollSmoother 3.13.0
 * https://gsap.com
 *
 * @license Copyright 2008-2025, GreenSock. All rights reserved.
 * Subject to the terms at https://gsap.com/standard-license
 * @author: Jack Doyle, <EMAIL>
*/var he,Os,wt,Wr,Ln,Lr,mi,zu,ee,wr,As,Fu,$u,Bu,Nu,kd=function(){return typeof window<"u"},Ld=function(){return he||kd()&&(he=window.gsap)&&he.registerPlugin&&he},Jm=function(e){return Math.round(e*1e5)/1e5||0},Yr=function(e){return ee.maxScroll(e||wt)},Zm=function(e,r){var i=e.parentNode||Ln,n=e.getBoundingClientRect(),s=i.getBoundingClientRect(),a=s.top-n.top,o=s.bottom-n.bottom,l=(Math.abs(a)>Math.abs(o)?a:o)/(1-r),u=-l*r,f,c;return l>0&&(f=s.height/(wt.innerHeight+s.height),c=f===.5?s.height*2:Math.min(s.height,Math.abs(-l*f/(2*f-1)))*2*(r||1),u+=r?-c*r:-c/2,l+=c),{change:l,offset:u}},Qm=function(e){var r=Wr.querySelector(".ScrollSmoother-wrapper");return r||(r=Wr.createElement("div"),r.classList.add("ScrollSmoother-wrapper"),e.parentNode.insertBefore(r,e),r.appendChild(e)),r},Fi=function(){function t(e){var r=this;Os||t.register(he)||console.warn("Please gsap.registerPlugin(ScrollSmoother)"),e=this.vars=e||{},wr&&wr.kill(),wr=this,Bu(this);var i=e,n=i.smoothTouch,s=i.onUpdate,a=i.onStop,o=i.smooth,l=i.onFocusIn,u=i.normalizeScroll,f=i.wholePixels,c,p,d,_,h,m,g,v,x,y,b,w,C,O,M=this,A=e.effectsPrefix||"",k=ee.getScrollFunc(wt),E=ee.isTouch===1?n===!0?.8:parseFloat(n)||0:o===0||o===!1?0:parseFloat(o)||.8,L=E&&+e.speed||1,R=0,H=0,F=1,B=Fu(0),j=function(){return B.update(-R)},Z={y:0},S=function(){return c.style.overflow="visible"},fe,Le=function(T){T.update();var D=T.getTween();D&&(D.pause(),D._time=D._dur,D._tTime=D._tDur),fe=!1,T.animation.progress(T.progress,!0)},Ke=function(T,D){(T!==R&&!y||D)&&(f&&(T=Math.round(T)),E&&(c.style.transform="matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, "+T+", 0, 1)",c._gsap.y=T+"px"),H=T-R,R=T,ee.isUpdating||t.isRefreshing||ee.update())},_e=function(T){return arguments.length?(T<0&&(T=0),Z.y=-T,fe=!0,y?R=-T:Ke(-T),ee.isRefreshing?_.update():k(T/L),this):-R},De=typeof ResizeObserver<"u"&&e.autoResize!==!1&&new ResizeObserver(function(){if(!ee.isRefreshing){var I=Yr(p)*L;I<-R&&_e(I),Nu.restart(!0)}}),je,Ee=function(T){p.scrollTop=0,!(T.target.contains&&T.target.contains(p)||l&&l(r,T)===!1)&&(ee.isInViewport(T.target)||T.target===je||r.scrollTo(T.target,!1,"center center"),je=T.target)},Je=function(T,D){if(T<D.start)return T;var V=isNaN(D.ratio)?1:D.ratio,W=D.end-D.start,U=T-D.start,ie=D.offset||0,ze=D.pins||[],Q=ze.offset||0,xe=D._startClamp&&D.start<=0||D.pins&&D.pins.offset?0:D._endClamp&&D.end===Yr()?1:.5;return ze.forEach(function(ce){W-=ce.distance,ce.nativeStart<=T&&(U-=ce.distance)}),Q&&(U*=(W-Q/V)/W),T+(U-ie*xe)/V-U},Ze=function I(T,D,V){V||(T.pins.length=T.pins.offset=0);var W=T.pins,U=T.markers,ie,ze,Q,xe,ce,ae,We,N;for(We=0;We<D.length;We++)if(N=D[We],T.trigger&&N.trigger&&T!==N&&(N.trigger===T.trigger||N.pinnedContainer===T.trigger||T.trigger.contains(N.trigger))&&(ce=N._startNative||N._startClamp||N.start,ae=N._endNative||N._endClamp||N.end,Q=Je(ce,T),xe=N.pin&&ae>0?Q+(ae-ce):Je(ae,T),N.setPositions(Q,xe,!0,(N._startClamp?Math.max(0,Q):Q)-ce),N.markerStart&&U.push(he.quickSetter([N.markerStart,N.markerEnd],"y","px")),N.pin&&N.end>0&&!V)){if(ie=N.end-N.start,ze=T._startClamp&&N.start<0,ze){if(T.start>0){T.setPositions(0,T.end+(T._startNative-T.start),!0),I(T,D);return}ie+=N.start,W.offset=-N.start}W.push({start:N.start,nativeStart:ce,end:N.end,distance:ie,trig:N}),T.setPositions(T.start,T.end+(ze?-N.start:ie),!0)}},lr=function(T,D){h.forEach(function(V){return Ze(V,T,D)})},Qe=function(){Ln=Wr.documentElement,Lr=Wr.body,S(),requestAnimationFrame(S),h&&(ee.getAll().forEach(function(T){T._startNative=T.start,T._endNative=T.end}),h.forEach(function(T){var D=T._startClamp||T.start,V=T.autoSpeed?Math.min(Yr(),T.end):D+Math.abs((T.end-D)/T.ratio),W=V-T.end;if(D-=W/2,V-=W/2,D>V){var U=D;D=V,V=U}T._startClamp&&D<0?(V=T.ratio<0?Yr():T.end/T.ratio,W=V-T.end,D=0):(T.ratio<0||T._endClamp&&V>=Yr())&&(V=Yr(),D=T.ratio<0||T.ratio>1?0:V-(V-T.start)/T.ratio,W=(V-D)*T.ratio-(T.end-T.start)),T.offset=W||1e-4,T.pins.length=T.pins.offset=0,T.setPositions(D,V,!0)}),lr(ee.sort())),B.reset()},et=function(){return ee.addEventListener("refresh",Qe)},oe=function(){return h&&h.forEach(function(T){return T.vars.onRefresh(T)})},Pe=function(){return h&&h.forEach(function(T){return T.vars.onRefreshInit(T)}),oe},at=function(T,D,V,W){return function(){var U=typeof D=="function"?D(V,W):D;U||U===0||(U=W.getAttribute("data-"+A+T)||(T==="speed"?1:0)),W.setAttribute("data-"+A+T,U);var ie=(U+"").substr(0,6)==="clamp(";return{clamp:ie,value:ie?U.substr(6,U.length-7):U}}},xt=function(T,D,V,W,U){U=(typeof U=="function"?U(W,T):U)||0;var ie=at("speed",D,W,T),ze=at("lag",V,W,T),Q=he.getProperty(T,"y"),xe=T._gsap,ce,ae,We,N,Vr,At,$t=[],kr=function(){D=ie(),V=parseFloat(ze().value),ce=parseFloat(D.value)||1,We=D.value==="auto",Vr=We||ae&&ae._startClamp&&ae.start<=0||$t.offset?0:ae&&ae._endClamp&&ae.end===Yr()?1:.5,N&&N.kill(),N=V&&he.to(T,{ease:As,overwrite:!1,y:"+=0",duration:V}),ae&&(ae.ratio=ce,ae.autoSpeed=We)},Qt=function(){xe.y=Q+"px",xe.renderTransform(1),kr()},ur=[],X=0,_r=function(pe){if(We){Qt();var we=Zm(T,zu(0,1,-pe.start/(pe.end-pe.start)));X=we.change,At=we.offset}else At=$t.offset||0,X=(pe.end-pe.start-At)*(1-ce);$t.forEach(function(Bt){return X-=Bt.distance*(1-ce)}),pe.offset=X||.001,pe.vars.onUpdate(pe),N&&N.progress(1)};return kr(),(ce!==1||We||N)&&(ae=ee.create({trigger:We?T.parentNode:T,start:function(){return D.clamp?"clamp(top bottom+="+U+")":"top bottom+="+U},end:function(){return D.value<0?"max":D.clamp?"clamp(bottom top-="+U+")":"bottom top-="+U},scroller:p,scrub:!0,refreshPriority:-999,onRefreshInit:Qt,onRefresh:_r,onKill:function(pe){var we=h.indexOf(pe);we>=0&&h.splice(we,1),Qt()},onUpdate:function(pe){var we=Q+X*(pe.progress-Vr),Bt=$t.length,fr=0,Ne,cr,q;if(pe.offset){if(Bt){for(cr=-R,q=pe.end;Bt--;){if(Ne=$t[Bt],Ne.trig.isActive||cr>=Ne.start&&cr<=Ne.end){N&&(Ne.trig.progress+=Ne.trig.direction<0?.001:-.001,Ne.trig.update(0,0,1),N.resetTo("y",parseFloat(xe.y),-H,!0),F&&N.progress(1));return}cr>Ne.end&&(fr+=Ne.distance),q-=Ne.distance}we=Q+fr+X*((he.utils.clamp(pe.start,pe.end,cr)-pe.start-fr)/(q-pe.start)-Vr)}ur.length&&!We&&ur.forEach(function(Y){return Y(we-fr)}),we=Jm(we+At),N?(N.resetTo("y",we,-H,!0),F&&N.progress(1)):(xe.y=we+"px",xe.renderTransform(1))}}}),_r(ae),he.core.getCache(ae.trigger).stRevert=Pe,ae.startY=Q,ae.pins=$t,ae.markers=ur,ae.ratio=ce,ae.autoSpeed=We,T.style.willChange="transform"),ae};et(),ee.addEventListener("killAll",et),he.delayedCall(.5,function(){return F=0}),this.scrollTop=_e,this.scrollTo=function(I,T,D){var V=he.utils.clamp(0,Yr(),isNaN(I)?r.offset(I,D,!!T&&!y):+I);T?y?he.to(r,{duration:E,scrollTop:V,overwrite:"auto",ease:As}):k(V):_e(V)},this.offset=function(I,T,D){I=mi(I)[0];var V=I.style.cssText,W=ee.create({trigger:I,start:T||"top top"}),U;return h&&(F?ee.refresh():lr([W],!0)),U=W.start/(D?L:1),W.kill(!1),I.style.cssText=V,he.core.getCache(I).uncache=1,U};function P(){return d=c.clientHeight,c.style.overflow="visible",Lr.style.height=wt.innerHeight+(d-wt.innerHeight)/L+"px",d-wt.innerHeight}this.content=function(I){if(arguments.length){var T=mi(I||"#smooth-content")[0]||console.warn("ScrollSmoother needs a valid content element.")||Lr.children[0];return T!==c&&(c=T,x=c.getAttribute("style")||"",De&&De.observe(c),he.set(c,{overflow:"visible",width:"100%",boxSizing:"border-box",y:"+=0"}),E||he.set(c,{clearProps:"transform"})),this}return c},this.wrapper=function(I){return arguments.length?(p=mi(I||"#smooth-wrapper")[0]||Qm(c),v=p.getAttribute("style")||"",P(),he.set(p,E?{overflow:"hidden",position:"fixed",height:"100%",width:"100%",top:0,left:0,right:0,bottom:0}:{overflow:"visible",position:"relative",width:"100%",height:"auto",top:"auto",bottom:"auto",left:"auto",right:"auto"}),this):p},this.effects=function(I,T){var D;if(h||(h=[]),!I)return h.slice(0);I=mi(I),I.forEach(function(ce){for(var ae=h.length;ae--;)h[ae].trigger===ce&&h[ae].kill()}),T=T||{};var V=T,W=V.speed,U=V.lag,ie=V.effectsPadding,ze=[],Q,xe;for(Q=0;Q<I.length;Q++)xe=xt(I[Q],W,U,Q,ie),xe&&ze.push(xe);return(D=h).push.apply(D,ze),T.refresh!==!1&&ee.refresh(),ze},this.sections=function(I,T){var D;if(m||(m=[]),!I)return m.slice(0);var V=mi(I).map(function(W){return ee.create({trigger:W,start:"top 120%",end:"bottom -20%",onToggle:function(ie){W.style.opacity=ie.isActive?"1":"0",W.style.pointerEvents=ie.isActive?"all":"none"}})});return T&&T.add?(D=m).push.apply(D,V):m=V.slice(0),V},this.content(e.content),this.wrapper(e.wrapper),this.render=function(I){return Ke(I||I===0?I:R)},this.getVelocity=function(){return B.getVelocity(-R)},ee.scrollerProxy(p,{scrollTop:_e,scrollHeight:function(){return P()&&Lr.scrollHeight},fixedMarkers:e.fixedMarkers!==!1&&!!E,content:c,getBoundingClientRect:function(){return{top:0,left:0,width:wt.innerWidth,height:wt.innerHeight}}}),ee.defaults({scroller:p});var ct=ee.getAll().filter(function(I){return I.scroller===wt||I.scroller===p});ct.forEach(function(I){return I.revert(!0,!0)}),_=ee.create({animation:he.fromTo(Z,{y:function(){return O=0,0}},{y:function(){return O=1,-P()},immediateRender:!1,ease:"none",data:"ScrollSmoother",duration:100,onUpdate:function(){if(O){var T=fe;T&&(Le(_),Z.y=R),Ke(Z.y,T),j(),s&&!y&&s(M)}}}),onRefreshInit:function(T){if(!t.isRefreshing){if(t.isRefreshing=!0,h){var D=ee.getAll().filter(function(W){return!!W.pin});h.forEach(function(W){W.vars.pinnedContainer||D.forEach(function(U){if(U.pin.contains(W.trigger)){var ie=W.vars;ie.pinnedContainer=U.pin,W.vars=null,W.init(ie,W.animation)}})})}var V=T.getTween();C=V&&V._end>V._dp._time,w=R,Z.y=0,E&&(ee.isTouch===1&&(p.style.position="absolute"),p.scrollTop=0,ee.isTouch===1&&(p.style.position="fixed"))}},onRefresh:function(T){T.animation.invalidate(),T.setPositions(T.start,P()/L),C||Le(T),Z.y=-k()*L,Ke(Z.y),F||(C&&(fe=!1),T.animation.progress(he.utils.clamp(0,1,w/L/-T.end))),C&&(T.progress-=.001,T.update()),t.isRefreshing=!1},id:"ScrollSmoother",scroller:wt,invalidateOnRefresh:!0,start:0,refreshPriority:-9999,end:function(){return P()/L},onScrubComplete:function(){B.reset(),a&&a(r)},scrub:E||!0}),this.smooth=function(I){return arguments.length&&(E=I||0,L=E&&+e.speed||1,_.scrubDuration(I)),_.getTween()?_.getTween().duration():0},_.getTween()&&(_.getTween().vars.ease=e.ease||As),this.scrollTrigger=_,e.effects&&this.effects(e.effects===!0?"[data-"+A+"speed], [data-"+A+"lag]":e.effects,{effectsPadding:e.effectsPadding,refresh:!1}),e.sections&&this.sections(e.sections===!0?"[data-section]":e.sections),ct.forEach(function(I){I.vars.scroller=p,I.revert(!1,!0),I.init(I.vars,I.animation)}),this.paused=function(I,T){return arguments.length?(!!y!==I&&(I?(_.getTween()&&_.getTween().pause(),k(-R/L),B.reset(),b=ee.normalizeScroll(),b&&b.disable(),y=ee.observe({preventDefault:!0,type:"wheel,touch,scroll",debounce:!1,allowClicks:!0,onChangeY:function(){return _e(-R)}}),y.nested=$u(Ln,"wheel,touch,scroll",!0,T!==!1)):(y.nested.kill(),y.kill(),y=0,b&&b.enable(),_.progress=(-R/L-_.start)/(_.end-_.start),Le(_))),this):!!y},this.kill=this.revert=function(){r.paused(!1),Le(_),_.kill();for(var I=(h||[]).concat(m||[]),T=I.length;T--;)I[T].kill();ee.scrollerProxy(p),ee.removeEventListener("killAll",et),ee.removeEventListener("refresh",Qe),p.style.cssText=v,c.style.cssText=x;var D=ee.defaults({});D&&D.scroller===p&&ee.defaults({scroller:wt}),r.normalizer&&ee.normalizeScroll(!1),clearInterval(g),wr=null,De&&De.disconnect(),Lr.style.removeProperty("height"),wt.removeEventListener("focusin",Ee)},this.refresh=function(I,T){return _.refresh(I,T)},u&&(this.normalizer=ee.normalizeScroll(u===!0?{debounce:!0,content:!E&&c}:u)),ee.config(e),"scrollBehavior"in wt.getComputedStyle(Lr)&&he.set([Lr,Ln],{scrollBehavior:"auto"}),wt.addEventListener("focusin",Ee),g=setInterval(j,250),Wr.readyState==="loading"||requestAnimationFrame(function(){return ee.refresh()})}return t.register=function(r){return Os||(he=r||Ld(),kd()&&window.document&&(wt=window,Wr=document,Ln=Wr.documentElement,Lr=Wr.body),he&&(mi=he.utils.toArray,zu=he.utils.clamp,As=he.parseEase("expo"),Bu=he.core.context||function(){},ee=he.core.globals().ScrollTrigger,he.core.globals("ScrollSmoother",t),Lr&&ee&&(Nu=he.delayedCall(.2,function(){return ee.isRefreshing||wr&&wr.refresh()}).pause(),Fu=ee.core._getVelocityProp,$u=ee.core._inputObserver,t.refresh=ee.refresh,Os=1))),Os},Km(t,[{key:"progress",get:function(){return this.scrollTrigger?this.scrollTrigger.animation._time/100:0}}]),t}();Fi.version="3.13.0";Fi.create=function(t){return wr&&t&&wr.content()===mi(t.content)[0]?wr:new Fi(t)};Fi.get=function(){return wr};Ld()&&he.registerPlugin(Fi);const e0={init(){ai.registerPlugin(te,Fi),Fi.create({smooth:1,effects:!0,wrapper:"#smooth-wrapper",content:"#smooth-content"})}};window.getToken=async()=>await fetch("https://api.platinumseed.com/!/DynamicToken/refresh").then(t=>t.json()).then(t=>t.csrf_token).catch(function(t){this.error="Something went wrong. Please try again later."});window.Alpine=nl;nl.plugin(jh);nl.start();Wh.init();e0.init();document.querySelector(".js-home-page")&&fullPage.init();document.querySelector(".js-homepage")&&d_.init();document.querySelector(".js-team-slider")&&jm.init();document.querySelector(".js-mobile-related-perspectives-slider")&&Wm.init();document.querySelector(".js-services-slider")&&qm.init();document.querySelector(".js-fade-in-up")&&Xm.init();document.querySelector(".js-slider-over")&&Um.init();
