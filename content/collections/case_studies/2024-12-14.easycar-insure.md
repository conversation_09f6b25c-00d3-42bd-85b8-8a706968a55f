---
id: 8d3720fb-9e13-4bc9-a889-91bbb035b4ff
blueprint: case_studies
title: easycar.insure
hero_title: 'A new option in car insurance quoting.'
brand_description: 'An insurance aggregator that provides consumers with the best options for their needs regarding car insurance. No obligation, no hooks and no significant data handover, just easy car insurance options tailored to every type of consumer.'
industry:
  - Insurance
contributers:
  - '<PERSON><PERSON>'
  - '<PERSON><PERSON>'
  - '<PERSON><PERSON><PERSON>'
  - '<PERSON>'
google_slide_embed_url: 'https://docs.google.com/presentation/d/e/2PACX-1vSbSKCAn7NqdBKRLsPrZeGaTchTpUITOED7hIpH3tKqTAhMptunFybKwM--_04eo1jEQQXDu841lE9K/embed?start=false&loop=false&delayms=3000'
article:
  -
    type: heading
    attrs:
      level: 2
    content:
      -
        type: text
        text: 'Who is easycar/easyquotes?'
  -
    type: paragraph
    content:
      -
        type: text
        text: 'easycar.insure is an insurance aggregator that provides consumers with the best options for their needs regarding car insurance. No obligation, no hooks and no significant data handover, just easy car insurance options tailored to every type of consumer.'
  -
    type: set
    attrs:
      id: m4l2etwc
      values:
        type: seperator
        dark_theme: false
  -
    type: heading
    attrs:
      level: 2
    content:
      -
        type: text
        text: 'What was the ask?'
  -
    type: paragraph
    content:
      -
        type: text
        text: "They didn’t have a website to house it all and effectively provide their services to the people of South Africa.\_"
  -
    type: paragraph
    content:
      -
        type: text
        text: "So we took their approach and defined user journeys for their target audience in the form of strategic site maps and considered wireframes - which culminated in the combining of that thinking and their CI guide to create a User Interface that landed the whole idea of easy and approachable brand and business engagement.\_"
  -
    type: paragraph
    content:
      -
        type: text
        text: 'And then we built the whole thing for them too. And don’t be fooled, developing and coding a website like this to be completely responsive is not as easy as our developers make it look.'
right_column_article:
  -
    type: heading
    attrs:
      level: 2
    content:
      -
        type: text
        text: 'How we delivered:'
  -
    type: paragraph
    content:
      -
        type: text
        text: 'Some key elements we delivered in the end product:'
  -
    type: paragraph
    content:
      -
        type: text
        text: 'User-friendly navigation with a clear user experience / Showcasing of fun and friendly brand identity / Lead-generating pre-built form integration / Combining valuable information relay and approachable UI.'
external_links:
  -
    id: m4l2gaoh
    title: 'Visit the website'
    url: 'https://easycar.insure/'
seo_title: easycar.insure
seo_description: 'An insurance aggregator that provides consumers with the best options for their needs regarding car insurance.'
seo_noindex: false
seo_nofollow: false
seo_canonical_type: entry
sitemap_change_frequency: weekly
sitemap_priority: 0.5
use_dark_header_theme: false
colour_scheme: easycar
tags:
  - website-design-development
feature_on_home_page: false
case_study_categories:
  - development
  - creative
feature_image:
  src: case-study-covers/responsive/easy-car-small.jpg
  ratio: '0.75'
  'glide:fit': crop_focal
  'sm:src': case-study-covers/responsive/easy-car-small.jpg
  'sm:ratio': '0.75'
  'sm:glide:fit': crop_focal
  'lg:src': case-study-covers/responsive/easy-car-large.jpg
  'lg:ratio': '3'
  'lg:glide:fit': crop_focal
listing_page_image:
  src: case-study-covers/responsive/easy-car-small.jpg
  ratio: '1.1'
updated_by: f091bb75-d170-4c39-ba1b-a16dbaf89422
updated_at: 1754919165
slide_show_pdf: ps_easycar_case-study_2024.pdf
og_image: casestudy_easycar_li.jpg
desktop_hero_image: case-study-heroes/easycar-desktop.webp
mobile_hero_image: case-study-heroes/easycar-mobile.webp
secondary_tags:
  - ux-ui-design
  - website-design
  - website-development
---
